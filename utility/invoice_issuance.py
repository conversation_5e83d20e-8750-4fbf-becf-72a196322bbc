import logging

from django.conf import settings

from pkg.amqp import ConsumerConfig
from pkg.amqp import MQWorker

logger = logging.getLogger("invoice")


@MQWorker(
    consumer_config=ConsumerConfig(
        queue=settings.ISSUANCE_QUEUE,
        prefetch_count=3,
    ),
    logger_=logger,
)
def main(data):
    """
    :params data:
        {
            "task_id": "6dcf2460-a3df-11eb-9f1e-acde48001122",
            "charge_detail_ids": [1, 2, 3],
            "exchange_rate": 1.3,
            "signing_entity": "开具实体",
            "invoice_currency_type": "CNY",
            "invoice_info_id": 1,
            "create_user": "admin",
        }
    """
    from income.invoice.mq import InvoiceIssuance

    logger.info(f"Received message: {data}")  # noqa: G004
    InvoiceIssuance(data=data).run()
