# ruff: noqa: PLR0913, N803, BLE001  # noqa: INP001
import logging
from concurrent.futures import ThreadPoolExecutor
from functools import partial
from functools import wraps

from django.conf import settings
from django.db import close_old_connections
from kombu import Connection
from kombu import Queue
from kombu.mixins import ConsumerProducerMixin
from kombu.utils.encoding import safe_repr

logger = logging.getLogger("rabbitmq")

pool = ThreadPoolExecutor(3)


class ConsumerConfig:
    """消费者配置"""

    __slots__ = (
        "accept",
        "auto_declare",
        "is_pickle",
        "no_ack",
        "prefetch_count",
        "queue",
    )

    def __init__(
        self,
        queue: Queue | str,
        no_ack: bool | None = None,
        auto_declare: bool | None = None,
        prefetch_count: int | None = None,
        accept: list[str] | None = None,
        *,
        is_pickle: bool | None = False,
    ):
        """

        :param queue: 队列名称
        :param no_ack: true: 自动确认
        :param auto_declare: 是否自动声明队列
        :param prefetch_count: 预取消息
        :param accept: 接收数据的格式 默认["json"],["pickle"]二进制
        :param is_pickle: 是否是二进制
        """
        if isinstance(queue, str):
            self.queue = Queue(queue)
        else:
            self.queue = queue
        self.no_ack = no_ack
        self.accept = (accept is None and ["json"]) or accept
        self.auto_declare = (auto_declare,)
        self.prefetch_count = prefetch_count
        self.is_pickle = is_pickle


class MQWorker(ConsumerProducerMixin):
    """
    producer:
    with MQWorker("amqp://admin:admin@127.0.0.1:5672//") as mq_worker:
    mq_worker.publish(
        body={"name": "test"},
        routing_key="test",
    )

    consumer:
    @MQWorker(
    rabbitmq_uri="amqp://admin:admin@127.0.0.1:5672//",
    consumer_config=ConsumerConfig(
        queue="test"
    ))
    def receive(message):
        pass
    """

    def __init__(
        self,
        rabbitmq_uri: str = settings.RABBITMQ,
        consumer_config: ConsumerConfig = None,
        logger_=None,
        *,
        bind: bool = False,
    ):
        self.logger = logger_ or logger
        self.bind = bind
        self.consumer_config = consumer_config
        self.connection = Connection(rabbitmq_uri, heartbeat=60)

    def get_consumers(self, Consumer, channel) -> list:
        return [
            Consumer(
                queues=self.consumer_config.queue,
                no_ack=self.consumer_config.no_ack,
                auto_declare=self.consumer_config.auto_declare,
                on_message=self.on_request,
                accept=self.consumer_config.accept,
                prefetch_count=self.consumer_config.prefetch_count,
            ),
        ]

    def on_request(self, message):
        pool.submit(self.task, message)

    def publish(self, body, **kwargs):
        self.logger.info("Publish message")
        self.logger.info(f"delivery_info: {kwargs}")  # noqa: G004
        self.logger.info(f"body: {body}")  # noqa: G004
        kwargs.setdefault("retry", True)  # 设置重试策略
        kwargs.setdefault("declare", [Queue(kwargs["routing_key"])])  # 声明发送队列
        self.producer.publish(body, **kwargs)
        self.logger.info("Publish Finish")

    def task(self, callback, message):
        self.logger.info("Received message")
        self.logger.info(f"delivery_info: {message.delivery_info}")  # noqa: G004
        if not self.consumer_config.is_pickle:
            # 跟外部对接mq时, 内容是json格式
            # 导致解析出错, 所以我们默认设置为json, 避免对接出问题。
            message.content_type = "application/json"
        try:
            decoded = message.decode()
        except Exception as exc:
            self.decode_error(message, exc)
        else:
            logger.info(f"body: {decoded}")  # noqa: G004
            close_old_connections()
            try:
                if self.bind:
                    callback(self, decoded)
                else:
                    callback(decoded)
            except Exception:
                logger.exception("Callback Error")
        finally:
            message.ack()
            logger.info("Ack Success")
        self.logger.info("Received Finish")

    def decode_error(self, message, exc):
        self.logger.error(
            "Can't decode message body: %r (type:%r encoding:%r raw:%r')",
            exc,
            message.content_type,
            message.content_encoding,
            safe_repr(message.body),
        )

    def __call__(self, callback):
        @wraps(callback)
        def inner():
            self.task = partial(self.task, callback)
            try:
                self.run()
            except KeyboardInterrupt:
                logger.info("KeyboardInterrupt")

        return inner

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.connection.close()
