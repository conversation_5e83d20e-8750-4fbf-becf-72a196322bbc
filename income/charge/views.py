from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import extend_schema_view
from rest_framework import mixins
from rest_framework.decorators import action
from rest_framework.response import Response

from income import const
from income.contrib.drf.filters import SearchAndFilter
from income.contrib.drf.views import GenericViewSet
from income.order.filters import CustomerNumFilter
from income.order.models import IncomeOrderInfo
from income.permissions import IsAuthenticated
from income.permissions import RoleMenuPermission

from .filters import AdjustTypeFilter
from .filters import IncomeChargeDetailFieldsFilter
from .models import IncomeAdjustDetail
from .models import IncomeChargeDetail
from .serializers import IncomeAdjustApproveSerializer
from .serializers import IncomeAdjustDetailSerializer
from .serializers import IncomeChargeAdjustCreateSerializer
from .serializers import IncomeChargeAdjustDetailSerializer
from .serializers import IncomeChargeDetailRawSerializer
from .serializers import IncomeOrderAdjustCreateSerializer
from .serializers import IncomeOrderAdjustSerializer
from .utils import CREATE_CHARGE_DETAIL_MAP
from .utils import add_extra_field
from .utils import adjust_export_to_excel
from .utils import export_to_excel
from .utils import income_adjust_add_extra_field


@extend_schema_view(
    list=extend_schema(summary="获取出账明细信息"),
)
@extend_schema(tags=["charge-detail"])
class IncomeChargeDetailViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
):
    serializer_class = IncomeChargeDetailRawSerializer
    filter_backends = [IncomeChargeDetailFieldsFilter]
    search_fields = ["order_no", "charge_month", "adjust_month"]
    search_contains = True
    table_mapping = {
        "order_no": "a",
        "charge_month": "a",
        "adjust_month": "d",
    }

    permission_classes = [IsAuthenticated, RoleMenuPermission]

    identify = const.MenuIdentify.INCOME_CHARGE

    def get_queryset(self):
        """
        使用Raw SQL获取完整数据, 减少内存使用
        """
        return self._get_raw_queryset()

    def _get_raw_queryset(self):
        """使用Raw SQL获取完整数据"""
        sql = """
        SELECT a.*,
               b.`customer_name`,
               c.`sale_name`,
               c.`sign_contract_entity`,
               c.`contract_legal_num`,
               d.`adjust_month`,
               d.`adjust_reason_class`,
               d.`adjust_reason`
        FROM `income_charge_detail` a
        LEFT JOIN `customer_info` b ON a.`customer_num` = b.`customer_num`
        LEFT JOIN `contract_info` c ON a.`contract_num` = c.`contract_num`
        LEFT JOIN `income_adjust_detail` d ON a.`income_adjust_id` = d.id
        ORDER BY a.`created_at` DESC
        """
        return IncomeChargeDetail.objects.raw(sql)

    @extend_schema(
        summary="导出出账明细数据为Excel文件",
        description="根据筛选条件导出出账明细数据为Excel文件",
        responses={200: {"description": "Excel文件下载"}},
    )
    @action(
        methods=["get"],
        detail=False,
        url_path="export",
        filter_backends=[IncomeChargeDetailFieldsFilter],
        pagination_class=None,
    )
    def export(self, request, *args, **kwargs):
        """导出出账明细数据为Excel文件"""
        # 获取筛选后的数据
        queryset = self.filter_queryset(self.get_queryset())

        # 转换为DataFrame并导出Excel
        return export_to_excel(queryset)


@extend_schema_view(
    list=extend_schema(summary="获取调账明细信息"),
)
@extend_schema(tags=["adjust-detail"])
class IncomeAdjustDetailViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
):
    """调整明细"""

    serializer_class = IncomeAdjustDetailSerializer
    filter_backends = [AdjustTypeFilter, SearchAndFilter]
    search_fields = ["order_no", "charge_month", "adjust_month"]
    search_contains = True
    adjust_type_choices = const.AdjustType

    permission_classes = [IsAuthenticated, RoleMenuPermission]

    identify = const.MenuIdentify.INCOME_ADJUST

    def get_queryset(self):
        return IncomeAdjustDetail.objects.all()

    @extend_schema(
        summary="导出调账明细数据为Excel文件",
        description="根据筛选条件导出调账明细数据为Excel文件",
        responses={200: {"description": "Excel文件下载"}},
    )
    @action(
        methods=["get"],
        detail=False,
        url_path="export",
        filter_backends=[AdjustTypeFilter, SearchAndFilter],
        pagination_class=None,
    )
    def export(self, request, *args, **kwargs):
        """导出调账明细数据为Excel文件"""
        # 获取筛选后的数据
        queryset = self.filter_queryset(self.get_queryset()).values(
            "batch_no",
            "order_no",
            "sub_order_no",
            "charge_month",
            "adjust_month",
            "adjust_amount",
            "adjust_tax",
            "adjust_reason_class",
            "adjust_reason",
            "state",
            "adjust_type",
            "create_user",
            "created_at",
        )

        # 转换为DataFrame并导出Excel
        return adjust_export_to_excel(queryset)

    @extend_schema(
        tags=["adjust-detail"],
        request=IncomeAdjustApproveSerializer,
        summary="调账记录的审批",
        description="审批调账记录, 审批通过 or 审批拒绝",
        responses={200: {"description": "审批成功"}},
    )
    @action(
        methods=["post"],
        detail=False,
        url_path="approve",
    )
    def approve(self, request, *args, **kwargs):
        """调账记录的审批"""

        serializer = IncomeAdjustApproveSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        adjust_instance = serializer.validated_data["adjust_instance"]
        action = serializer.validated_data["action"]
        adjust_type = serializer.validated_data["adjust_type"]

        # 根据不同的操作更新状态
        if action == const.AdjustApproveAction.APPROVE:
            adjust_instance.state = const.AdjustState.APPROVED
        else:
            adjust_instance.state = const.AdjustState.REJECTED

        # 添加审批人信息
        adjust_instance.approve_user = request.user.username
        adjust_instance.save(update_fields=["state", "approve_user"])

        if action == const.AdjustApproveAction.APPROVE:
            # 审批通过后新建权责信息
            CREATE_CHARGE_DETAIL_MAP[adjust_type](
                {
                    "adjust_amount": adjust_instance.adjust_amount,
                    "charge_month": adjust_instance.charge_month,
                    "charge_detail_id": adjust_instance.adjust_charge_detail_id,
                    "adjust_id": adjust_instance.id,
                    # 对应订单表中的total_num
                    "total_num": adjust_instance.sub_order_no,
                },
            )
        return Response(data="审批操作完成")


@extend_schema_view(
    list=extend_schema(summary="获取出账调账明细信息"),
    adjust_account=extend_schema(summary="调账出账明细"),
)
@extend_schema(tags=["charge-adjust-detail"])
class IncomeChargeAdjustDetailViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
):
    """出账调账明细"""

    serializer_class = IncomeChargeAdjustDetailSerializer
    serializers = {
        "adjust_account": IncomeChargeAdjustCreateSerializer,
    }
    filter_backends = [SearchAndFilter, CustomerNumFilter]
    search_fields = ["sub_order_no", "charge_month"]
    search_contains = True

    permission_classes = [IsAuthenticated, RoleMenuPermission]

    identify = const.MenuIdentify.INCOME_ADJUST

    def get_queryset(self):
        return IncomeChargeDetail.objects.filter(income_adjust_id__isnull=True)

    def get_serializer_class(self):
        return self.serializers.get(self.action, self.serializer_class)

    def list(self, request, *args, **kwargs):
        fields = (
            "id",
            "batch_no",
            "order_no",
            "sub_order_no",
            "charge_month",
            "fee_amount",
            "income_type",
            "tax",
            "tax_type",
            "customer_num",
        )
        queryset = self.filter_queryset(
            self.get_queryset(),
        ).values(*fields)
        page = self.paginate_queryset(queryset)
        if page is not None:
            data = add_extra_field(page)
            return self.get_paginated_response(data)
        data = add_extra_field(queryset)
        return Response(data)

    @action(
        methods=["post"],
        detail=False,
        url_path="adjust-account",
    )
    def adjust_account(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)


@extend_schema_view(
    list=extend_schema(summary="获取订单调账的信息"),
)
@extend_schema(tags=["order-adjust"])
class IncomeOrderAdjustViewInfoViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
):
    serializer_class = IncomeOrderAdjustSerializer
    serializers = {
        "adjust_account": IncomeOrderAdjustCreateSerializer,
    }
    filter_backends = [SearchAndFilter]
    permission_classes = [IsAuthenticated, RoleMenuPermission]

    search_fields = ["order_num", "total_num"]
    search_contains = True

    identify = const.MenuIdentify.INCOME_ADJUST

    def get_queryset(self):
        return IncomeOrderInfo.objects.filter(
            bill_status__in=[
                const.OrderBillStatus.IN_PROGRESS,
                const.OrderBillStatus.DECOM_BILLING_CONFIRMED,
                const.OrderBillStatus.CHG_DECOM_BILLING_CONFIRMED,
            ],
        )

    def get_serializer_class(self):
        return self.serializers.get(self.action, self.serializer_class)

    def list(self, request, *args, **kwargs):
        fields = (
            "id",
            "order_num",
            "total_num",
            "bill_status",
            "income_type",
            "tax_type",
            "tax_rate",
            "contract_num",
        )
        queryset = self.filter_queryset(
            self.get_queryset(),
        ).values(*fields)
        page = self.paginate_queryset(queryset)
        if page is not None:
            data = income_adjust_add_extra_field(page)
            return self.get_paginated_response(data)
        data = income_adjust_add_extra_field(queryset)
        return Response(data)

    @action(
        methods=["post"],
        detail=False,
        url_path="adjust-account",
    )
    def adjust_account(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)
