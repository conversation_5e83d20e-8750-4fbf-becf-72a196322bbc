from income import const

# 提示信息配置文件
# -----------------------------------------------------------------------------
NOT_FOUND = "Not Found"

USERNAME_OR_PASSWORD_ERROR = "用户名或密码有误"  # noqa: S105
LOGIN_SUCCESS = "登录成功"
LOGOUT_SUCCESS = "登出成功"

# 客户管理
CUSTOMER_NOT_FOUND = "该客户信息不存在或您无权访问"
SALE_NAME_NOT_FOUND = "该销售不存在请重新进行选择"
CONTACT_NOT_FOUND = "该客户联系人信息不存在或您无权访问"
CUSTOMER_APPROVE_SUBMIT_ERROR = "只有初始状态的客户信息才能提交审核"
CUSTOMER_APPROVE_REVOKE_ERROR = "只有待审核状态的客户信息才能撤回"
CUSTOMER_APPROVE_APPROVE_ERROR = "只有待审核状态的客户信息才能审批或驳回"

# 角色
ROLE_NOT_FOUND = "请选择合规的角色"
ROLE_CODE_ERROR = "角色编码不能设置为admin"

# 菜单
MENU_NOT_FOUND = "请选择合规的菜单"

# 部门
DEPARTMENT_NOT_FOUND = "不存在该部门或您无权访问"

# 分账序号
ACCOUNT_SEQ_EXISTS = "该客户下存在相同的分账序号,请重新输入"
ACCPUNT_SEQ_NOT_FOUND = "该分账序号不存在或您无权访问"

# 合同
CONTRACT_NOT_FOUND = "该合同信息不存在或您无权访问"

# 订单
ORDER_TOTAL_NUM_EXISTS = "合成编号重复,请重新生成"
ORDER_SERVICE_STATUS_NOT_MODIFY = "只有新装暂存订单才可支持编辑操作"
ORDER_SERVICE_STATUS_NOT_CHANGE_BILL_STATUS = "当前的服务状态的订单不支持计费审核"
ORDER_NOT_FOUND = "该订单信息不存在或您无权访问"
ORDER_FILE_NOT_FOUND = "该订单文件不存在或您无权访问"
ORDER_SERVICE_STATUS_NOT_DECOM_ROLLBACK = "只有服务中和计费中的订单才可进行拆机回退操作"

# 发票信息
INVOICE_INFO_NOT_FOUND = "该发票信息不存在或您无权访问"
INVALID_DATE_RANGE = "开账开始日期不能晚于结束日期"
INV_INFO_ERROR_MSG = {
    "email_address": "邮箱地址不能为空",
    "postal_address": "邮寄地址不能为空",
}
INV_INFO_NOT_ADJUST_PRIORITY = "当前发票信息无法进行调整优先级"

# 费用
FEE_PACKAGE_NAME_EXISTS = "套餐名称已存在"
FEE_INSTANCE_NOT_FOUND = "套餐实例不存在或您无权访问"
FEE_PACKAGE_NOT_FOUND = "套餐不存在或您无权访问"
FEE_TEMPLATE_NOT_FOUND = "套餐模板不存在或您无权访问"
FEE_INSTANCE_LEVEL_NOT_FOUND = "费用实例等级不存在或您无权访问"
FEE_TEMPLATE_NOT_LEVEL_FEE = "该实例的费用模板不支持阶梯计费,无法创建费用实例等级"
FEE_INSTANCE_HAS_LEVEL_DATA = "该费用实例下存在等级信息,不能修改为非阶梯计费模板"

# 调账
CHARGE_DETAIL_NOT_FOUND = "该权责信息不存在或您无权访问"
ADJUST_DETAIL_NOT_FOUND = "该调账记录不存在或您无权访问"
CHARGE_APPROVE_SUBMIT_ERROR = "只有初始化导入的记录才能进行审批"

# 对账
BANK_STATEMENT_NOT_FOUND = "该银行流水不存在、没有绑定合同或您无权访问"
AMOUNT_MUST_BE_POSITIVE = "金额必须为正数"
AMOUNT_EXCEEDS_BANK_STATEMENT = "认款金额不能大于银行流水剩余金额"
INVOICE_AMOUNT_NOT_MATCH = "认款金额不能大于发票剩余金额"
TMP_IDS_NOT_FOUND = "部分认款信息不存在或已提交审批"

# 发票
INVALID_CHARGE_MONTH_RANGE = "起始账期不能晚于结束账期"
ACCOUNT_SEQ_NOT_SAME = "权责信息的分账序号不相同, 不能开具同一张发票"
INVOICE_NOT_INVOICING = "开票的发票信息中存在不支持开票的数据"
INVOICE_NOT_FOUND = "该发票不存在或您无权访问"
INVOICE_ISSUANCE_SUCCESS = {
    const.InvoiceIssuanceAction.ISSUANCE: "开票成功",
    const.InvoiceIssuanceAction.PAUSE: "设置暂不开票成功",
}
ATTACHMENT_FILE_SIZE_ERROR = "附件大小超出限制"
MULTI_MEDIA_SUFFIX_ERROR = "附件格式不支持,仅支持pdf格式"
INV_TYPE_NOT_SUPPORT_ATTACHMENT = "该发票不支持上传附件"

INVOICE_DETAIL_NOT_FOUND = "该发票详情不存在或您无权访问"

INVOICE_NOT_FOUND_OR_INVALID_STATE = "发票不存在或状态不正确"
ATTACHMENT_FILE_NOT_FOUND = "附件不存在,请联系管理员查看"
