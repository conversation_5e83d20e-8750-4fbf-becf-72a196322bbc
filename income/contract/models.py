from django.db import models

from income import const


class ContractInfo(models.Model):
    """合同信息表"""

    contract_num = models.CharField(
        unique=True,
        max_length=32,
        db_comment="合同编号",
        error_messages={"unique": "合同编号已存在, 请重新输入"},
    )
    contract_title = models.CharField(max_length=128, db_comment="合同标题")
    business = models.CharField(max_length=128, db_comment="业务类型")
    alias_customer_num = models.CharField(
        max_length=32,
        blank=True,
        null=True,
        db_comment="客户别名",
    )
    contract_type = models.CharField(
        max_length=32,
        choices=const.ContractType.choices,
        db_comment="合同类型",
    )
    sale_name = models.CharField(max_length=32, db_comment="销售")
    frame_contract_num = models.CharField(
        max_length=32,
        blank=True,
        null=True,
        db_comment="框架合同ID",
    )
    frame_legal_num = models.Char<PERSON>ield(
        max_length=32,
        blank=True,
        null=True,
        db_comment="框架合同法务编号",
    )
    frame_oa_num = models.CharField(
        max_length=32,
        blank=True,
        null=True,
        db_comment="框架合同OA编号",
    )
    main_customer_num = models.CharField(max_length=32, db_comment="客户主合同方")
    sign_contract_entity = models.CharField(max_length=64, db_comment="263签约方")
    contract_legal_num = models.CharField(
        max_length=64,
        blank=True,
        null=True,
        db_comment="合同法务编号",
        default="/",
    )
    contract_oa_num = models.CharField(
        max_length=32,
        blank=True,
        null=True,
        db_comment="合同OA编号",
    )
    other_party_num = models.CharField(
        max_length=64,
        blank=True,
        null=True,
        db_comment="合同对方编号",
    )
    archive_file_path = models.CharField(
        max_length=512,
        blank=True,
        null=True,
        db_comment="归档文件路径",
    )
    remark = models.TextField(blank=True, null=True, db_comment="合同备注")
    contract_start_date = models.DateField(db_comment="合同生效日期")
    is_auto_delay = models.CharField(max_length=32, db_comment="是否自动延期")
    auto_delay_num = models.IntegerField(db_comment="自动延期次数")
    contract_month_amount = models.DecimalField(
        max_digits=14,
        decimal_places=2,
        default=0.00,
        db_comment="合同月金额",
    )
    contract_summary = models.CharField(
        max_length=1024,
        blank=True,
        null=True,
        db_comment="合同概述",
    )
    append_contract_way = models.CharField(
        max_length=256,
        blank=True,
        null=True,
        db_comment="客户附加合同方",
    )
    append_contract_explain = models.CharField(
        max_length=512,
        blank=True,
        null=True,
        db_comment="多方合同说明",
    )
    group_approve_state = models.CharField(
        max_length=25,
        blank=True,
        null=True,
        db_comment="集团审批结果",
    )

    create_user = models.CharField(max_length=32, db_comment="创建者")
    state = models.CharField(max_length=20, db_comment="合同状态")

    contract_term = models.IntegerField(
        choices=const.ContractTerm.choices,
        default=const.ContractTerm.FIXED,
        db_comment="合同期限: 0:固定期限合同; 1:无固定期限合同",
    )
    contract_object = models.CharField(
        max_length=30,
        blank=True,
        null=True,
        db_comment="合同标的",
    )
    contract_end_date = models.DateField(
        blank=True,
        null=True,
        db_comment="合同失效日期",
    )
    contact_person = models.CharField(
        max_length=50,
        db_comment="客户联系人姓名",
    )
    contact_address = models.CharField(
        max_length=400,
        db_comment="客户联系地址",
    )
    contact_telephone = models.CharField(
        max_length=40,
        db_comment="客户联系电话",
    )
    contact_email = models.EmailField(
        max_length=50,
        db_comment="客户联系邮箱",
    )
    bank_account_number = models.CharField(
        max_length=100,
        db_comment="银行账号",
    )
    bank_account_name = models.CharField(
        max_length=255,
        db_comment="银行户名(公司名/人名)",
    )
    bank_name = models.CharField(
        max_length=255,
        db_comment="开户行名称",
    )
    applicant_name = models.EmailField(
        max_length=255,
        db_comment="审批人邮箱",
    )

    created_at = models.DateTimeField(db_comment="创建时间", auto_now_add=True)
    updated_at = models.DateTimeField(db_comment="更新时间", auto_now=True)

    class Meta:
        managed = False
        db_table = "contract_info"
        db_table_comment = "合同信息表"
        ordering = ("-created_at",)

    def __str__(self):
        return f"<ContractInfo: {self.contract_title}>"


class ContractFile(models.Model):
    """合同文件表"""

    contract_id = models.IntegerField(db_comment="合同id")
    file_name = models.CharField(
        max_length=256,
        blank=True,
        null=True,
        db_comment="文件名称",
    )
    save_file_name = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        db_comment="保存的文件名称",
    )
    file_path = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        db_comment="存放的本地路径",
    )
    upload_s3 = models.IntegerField(
        blank=True,
        null=True,
        default=0,
        db_comment="是否已上传上s3 0:未上传; 1:已上传",
    )
    s3_file_path = models.CharField(
        max_length=512,
        blank=True,
        null=True,
        db_comment="s3文件路径",
    )
    status = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        choices=const.ContractFileStatus.choices,
        default=const.ContractFileStatus.UNSIGNED,
        db_comment="文件状态: 初始-unsigned/归档-signed",
    )
    create_user = models.CharField(
        max_length=32,
        blank=True,
        null=True,
        db_comment="创建者",
    )
    created_at = models.DateTimeField(db_comment="创建时间", auto_now_add=True)

    class Meta:
        managed = False
        db_table = "contract_file"
        db_table_comment = "合同文件表"
        ordering = ("-created_at",)

    def __str__(self):
        return f"<ContractFile: {self.file_name}>"
