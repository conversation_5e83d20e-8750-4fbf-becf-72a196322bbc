from django.urls import include
from django.urls import path
from rest_framework.routers import SimpleRouter

from .views import ContractFileViewSet
from .views import ContractInfoViewSet

# 合同信息
router = SimpleRouter(trailing_slash=False)
router.register("contracts", ContractInfoViewSet, basename="contract_info")

# 合同文件
file_router = SimpleRouter(trailing_slash=False)
file_router.register("contract-files", ContractFileViewSet, basename="contract_file")

urlpatterns = [
    path("customers/", include(router.urls)),
    path("contracts/<int:contract_id>/", include(file_router.urls)),
]
