from rest_framework.permissions import BasePermission

from income import message
from income.contrib.drf.shortcuts import get_object_or_404

from .models import ContractInfo


class ConrtactPermission(BasePermission):
    """合同信息权限"""

    def has_permission(self, request, view):
        get_object_or_404(
            ContractInfo.objects.all(),
            pk=view.kwargs["contract_id"],
            error_message=message.CONTRACT_NOT_FOUND,
        )
        return True
