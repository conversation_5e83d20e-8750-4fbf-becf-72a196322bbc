from income.customer.models import CustomerInfo


def add_extra_field(query_data_list: list):
    """添加客户名称

    :param query_data_list:
    :return:
    """
    customer_nums = [
        query_data["main_customer_num"] for query_data in query_data_list
    ]
    customer_qs = CustomerInfo.objects.filter(
        customer_num__in=set(customer_nums),
    ).values_list("customer_num", "customer_name")
    customer_map = dict(customer_qs)
    for query_data in query_data_list:
        query_data["customer_name"] = customer_map.get(
            query_data.get("main_customer_num"),
        )
        query_data["created_at"] = query_data["created_at"].strftime("%s")
    return query_data_list
