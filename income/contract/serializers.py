from django.db import transaction
from rest_framework import serializers

from income import message
from income.customer.models import CustomerInfo
from income.utils.file_handler import validate_and_save_file

from .models import ContractFile
from .models import ContractInfo


class ContractSerializer(serializers.ModelSerializer):
    contract_files = serializers.ListField(
        child=serializers.FileField(allow_empty_file=False, use_url=False),
        write_only=True,
        required=True,
        help_text="合同文件",
    )

    class Meta:
        model = ContractInfo
        exclude = ("updated_at", "archive_file_path")
        read_only_fields = ("create_user", "group_approve_state")

    def validate_main_customer_num(self, value):
        """校验客户编码是否存在"""
        if self.instance and self.instance.main_customer_num == value:
            return value

        if not CustomerInfo.objects.filter(customer_num=value).exists():
            raise serializers.ValidationError(message.CUSTOMER_NOT_FOUND)
        return value

    def validate_contract_files(self, value):
        """校验合同文件"""
        file_info_list = []
        for file in value:
            result = validate_and_save_file(
                file=file,
                category="contract",  # 使用预定义的类别
                allowed_types=["pdf", "word", "excel", "image"],  # 限制文件类型
                max_size=20 * 1024 * 1024,  # 20MB限制
                check_duplicates=False,  # 重复检查
                check_security=True,  # 安全检查
            )
            if not result["success"]:
                # 如果有警告,记录日志
                if result.get("warnings"):
                    import logging

                    logger = logging.getLogger("file_handler")
                    for warning in result["warnings"]:
                        logger.warning(f"文件上传警告: {warning}")  # noqa: G004
                raise serializers.ValidationError(result["errors"])
            file_info_list.append(result["file_info"])
        self.file_info_list = file_info_list
        return value

    @staticmethod
    def bulk_create_contract_file(contract_instance, create_user, file_info_list):
        """批量创建合同文件

        :param contract_instance: 合同实例
        :param create_user: 创建者
        :param file_info_list: 文件信息列表
        :return:
        """
        ContractFile.objects.bulk_create(
            [
                ContractFile(
                    contract_id=contract_instance.id,
                    file_name=file_info["original_name"],
                    save_file_name=file_info["saved_name"],
                    file_path=file_info["relative_path"],
                    create_user=create_user,
                )
                for file_info in file_info_list
            ],
            batch_size=50,
        )

    def create(self, validated_data):
        create_user = self.context["request"].user.username
        validated_data["create_user"] = create_user
        validated_data.pop("contract_files")
        with transaction.atomic():
            contract_instance = self.Meta.model.objects.create(**validated_data)
            self.bulk_create_contract_file(
                contract_instance,
                create_user,
                self.file_info_list,
            )
        return contract_instance

    def update(self, instance, validated_data):
        create_user = self.context["request"].user.username
        validated_data["create_user"] = create_user
        contract_files = validated_data.pop("contract_files")
        with transaction.atomic():
            for k, v in validated_data.items():
                setattr(instance, k, v)
            instance.save()
            if contract_files:
                self.bulk_create_contract_file(
                    instance,
                    create_user,
                    self.file_info_list,
                )
        return instance


class ContractSimpleSerializer(serializers.ModelSerializer):

    class Meta:
        model = ContractInfo
        fields = (
            "id",
            "contract_title",
            "contract_num",
            "main_customer_num",
        )


class ContractFileSerializer(serializers.ModelSerializer):
    class Meta:
        model = ContractFile
        fields = (
            "id",
            "file_name",
            "status",
        )
