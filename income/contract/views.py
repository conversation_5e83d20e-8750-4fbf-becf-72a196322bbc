from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import extend_schema_view
from rest_framework import mixins
from rest_framework.decorators import action
from rest_framework.response import Response

from income import const
from income import message
from income.contrib.drf.filters import SearchFilter
from income.contrib.drf.views import CreateModelMixin
from income.contrib.drf.views import GenericViewSet
from income.contrib.drf.views import UpdateModelMixin
from income.permissions import IsAuthenticated
from income.permissions import RoleMenuPermission
from income.utils.file_handler import file_preview

from .filters import CustomerNumberFilter
from .models import ContractFile
from .models import ContractInfo
from .permissions import ConrtactPermission
from .serializers import ContractFileSerializer
from .serializers import ContractSerializer
from .serializers import ContractSimpleSerializer
from .utils import add_extra_field


@extend_schema_view(
    list=extend_schema(summary="获取合同信息"),
    create=extend_schema(summary="新建合同信息"),
    update=extend_schema(summary="编辑合同信息"),
    simple_list=extend_schema(
        summary="仅展示合同信息ID、合同标题、合同编号、客户编号",
        responses={200: ContractSimpleSerializer(many=True)},
    ),
)
@extend_schema(tags=["contract"])
class ContractInfoViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
    CreateModelMixin,
    UpdateModelMixin,
):
    """合同信息"""

    NOT_FOUND_MESSAGE = message.CONTRACT_NOT_FOUND

    serializer_class = ContractSerializer
    permission_classes = (IsAuthenticated, RoleMenuPermission)

    search_fields = [
        "contract_title",
        "contract_num",
        "create_user",
        "main_customer_num",
    ]
    search_contains = True
    filter_backends = (SearchFilter, CustomerNumberFilter)

    identify = const.MenuIdentify.CONTRACT_INFO

    def get_queryset(self):
        return ContractInfo.objects.all()

    def list(self, request, *args, **kwargs):
        fields = [field.name for field in ContractInfo._meta.fields]  # noqa: SLF001
        fields.remove("updated_at")
        queryset = self.filter_queryset(
            self.get_queryset(),
        ).values(*fields)
        page = self.paginate_queryset(queryset)
        if page is not None:
            data = add_extra_field(page)
            return self.get_paginated_response(data)
        data = add_extra_field(queryset)
        return Response(data)

    @action(
        methods=["get"],
        detail=False,
        url_path="simple-list",
        filter_backends=[],
        pagination_class=None,
    )
    def simple_list(self, request, *args, **kwargs):
        queryset = (
            self.get_queryset()
            .filter(
                contract_type__in=[
                    const.ContractType.ORDER,
                    const.ContractType.COMPREHENSIVE,
                ],
            )
            .values(
                "id",
                "contract_num",
                "contract_title",
                "main_customer_num",
            )
        )
        return Response(queryset)


@extend_schema_view(
    list=extend_schema(summary="获取合同文件信息"),
    attachment_preview=extend_schema(
        summary="预览附件",
        responses={200: {"description": "FileStream"}},
    ),
)
@extend_schema(tags=["contract-file"])
class ContractFileViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
):
    serializer_class = ContractFileSerializer
    permission_classes = (IsAuthenticated, RoleMenuPermission, ConrtactPermission)
    pagination_class = None

    identify = const.MenuIdentify.CONTRACT_INFO

    def get_queryset(self):
        return ContractFile.objects.filter(contract_id=self.kwargs["contract_id"])

    @action(
        methods=["get"],
        detail=True,
        url_path="attachment-preview",
    )
    def attachment_preview(self, request, *args, **kwargs):
        """预览附件"""
        # 获取合同文件对象
        contract_file = self.get_object()
        file_info = ContractFile.objects.values("file_path", "file_name").get(
            id=contract_file.id,
        )
        return file_preview(file_info["file_name"], file_info["file_path"])
