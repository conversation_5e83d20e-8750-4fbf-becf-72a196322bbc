from collections import defaultdict

from django.contrib.auth.base_user import AbstractBaseUser
from django.contrib.auth.base_user import BaseUserManager
from django.db import models

from income import const


class UserDepartment(models.Model):
    department_name = models.CharField(
        unique=True,
        max_length=100,
        db_comment="部门名称",
        error_messages={
            "unique": "部门名称已存在,请输入不同的名称",
        },
    )
    parent_id = models.IntegerField(
        db_comment="父部门ID(自关联)", blank=True, null=True,
    )
    created_at = models.DateTimeField("创建时间", auto_now_add=True)
    updated_at = models.DateTimeField("更新时间", auto_now=True)

    class Meta:
        managed = False
        db_table = "user_department"
        db_table_comment = "用户组织架构表"
        ordering = ("-created_at",)

    def __str__(self):
        return f"<UserDepartment: {self.department_name}>:"


class UserManager(BaseUserManager):
    use_in_migrations = True

    def create_user(self, username, email, mobile, password, **kwargs):
        user = self.model(
            username=username, email=email, mobile=mobile, state=1, **kwargs,
        )
        user.set_password(password)

        user.save()

        return user


class User(AbstractBaseUser):
    department_id = models.IntegerField(db_comment="部门ID")
    password = models.CharField(db_comment="密码", max_length=255)
    last_login_time = models.DateTimeField(
        db_comment="最后登录时间", blank=True, null=True,
    )
    last_login_ip = models.CharField(
        db_comment="最后登录IP", max_length=45, blank=True, null=True,
    )
    effective_time = models.DateTimeField(db_comment="生效时间", blank=True, null=True)
    expire_time = models.DateTimeField(verbose_name="过期时间", blank=True, null=True)
    sign_in_num = models.IntegerField(db_comment="用户名", blank=True, null=True)
    username = models.CharField(
        db_comment="用户名",
        unique=True,
        max_length=50,
        error_messages={
            "unique": "用户名称已存在,请输入不同的名称",
        },
    )
    user_type = models.CharField(
        db_comment="用户类型",
        max_length=20,
        blank=True,
        null=True,
    )
    email = models.CharField(
        db_comment="邮箱",
        unique=True,
        max_length=100,
        error_messages={
            "unique": "邮箱已被使用,请重新输入",
        },
    )
    mobile = models.CharField(
        db_comment="手机号",
        unique=True,
        max_length=20,
        error_messages={
            "unique": "手机号已被使用,请重新输入",
        },
    )
    state = models.IntegerField(
        choices=const.UserState.choices,
        default=const.UserState.ENABLE,
        db_comment="状态(0=禁用,1=启用,2=冻结,3=注销)",
    )

    created_at = models.DateTimeField("创建时间", auto_now_add=True)
    updated_at = models.DateTimeField("更新时间", auto_now=True)

    objects = UserManager()

    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = ["username", "mobile"]
    last_login = None

    class Meta:
        managed = False
        db_table = "user"
        db_table_comment = "用户信息表"
        ordering = ("-created_at",)

    def __str__(self):
        """
        Return a string representation of the User instance.

        This representation includes the username of the user and is used
        in the Django admin interface and other display contexts.
        """
        return f"<User: {self.username}>"

    __repr__ = __str__

    # 获取用户的角色
    @property
    def is_admin(self):
        """是否为管理员"""
        role_ids = UserRole.objects.filter(user_id=self.id).values_list(
            "role_id", flat=True,
        )
        return Role.objects.filter(
            id__in=role_ids, role_code=const.SUPER_ADMIN,
        ).exists()

    @property
    def user_roles(self) -> list:
        return list(
            UserRole.objects.filter(user_id=self.id).values_list("role_id", flat=True),
        )


class Menu(models.Model):
    menu_name = models.CharField(max_length=100, db_comment="菜单名称")
    router = models.CharField(
        max_length=255, blank=True, null=True, db_comment="菜单路由",
    )
    icon = models.CharField(max_length=50, blank=True, null=True, db_comment="菜单图标")
    identify = models.CharField(unique=True, max_length=20)
    parent_id = models.IntegerField(
        blank=True, null=True, db_comment="父菜单ID(自关联)",
    )

    created_at = models.DateTimeField("创建时间", auto_now_add=True)

    class Meta:
        managed = False
        db_table = "menu"
        db_table_comment = "系统菜单表"
        ordering = ("identify",)

    def __str__(self):
        return f"<Menu: {self.menu_name}>"


class Role(models.Model):
    role_name = models.CharField(
        unique=True,
        max_length=50,
        db_comment="角色名称",
        error_messages={
            "unique": "角色名称已存在,请输入不同的名称",
        },
    )
    role_code = models.TextField(db_comment="角色编码")

    created_at = models.DateTimeField(db_comment="创建时间", auto_now_add=True)
    updated_at = models.DateTimeField(db_comment="更新时间", auto_now=True)

    class Meta:
        managed = False
        db_table = "role"
        db_table_comment = "基础角色表"
        ordering = ("-created_at",)

    def __str__(self):
        return f"<Role: {self.role_name}>"

    @property
    def get_menus(self):
        """获取当前角色被授权的菜单信息"""

        if self.role_code == const.SUPER_ADMIN:
            menu_queryset = Menu.objects.exclude(menu_name="ALL").values(
                "id", "menu_name", "icon", "parent_id",
            )
            menu_queryset = [
                {**item, "operation": item["parent_id"] is not None}
                for item in menu_queryset
            ]
        else:
            role_menus = RoleMenu.objects.filter(role_id=self.id).values_list(
                "menu_id", "operation",
            )
            # 获取当前角色的所有菜单信息
            menu_queryset = Menu.objects.filter(
                id__in=[rm[0] for rm in role_menus],
            ).values("id", "menu_name", "icon", "parent_id")
            # 生成{"menu_id": "operation"}的映射关系
            mapping = dict(role_menus)
            for menu in menu_queryset:
                menu["operation"] = mapping[menu["id"]]

        menus = []
        parent_map = defaultdict(list)  # 父级id对应的子级列表
        for menu in menu_queryset:
            parent_id = menu["parent_id"]
            data_dict = {
                "id": menu["id"],
                "menu_name": menu["menu_name"],
                "icon": menu["icon"],
                "operation": menu["operation"],
            }
            if parent_id is None:
                menus.append(data_dict)
            else:
                parent_map[parent_id].append(data_dict)

        def get_child_data(info):
            child_data_list = parent_map.get(info["id"])
            info["children"] = child_data_list or None
            if child_data_list:
                for child_data in child_data_list:
                    get_child_data(child_data)

        for menu in menus:
            get_child_data(menu)

        return menus


class RoleMenu(models.Model):
    role_id = models.IntegerField(db_comment="角色ID")
    menu_id = models.IntegerField(db_comment="菜单ID")
    operation = models.BooleanField(
        db_comment="是否可以操作", default=True, blank=True, null=True,
    )

    created_at = models.DateTimeField(db_comment="创建时间", auto_now_add=True)
    updated_at = models.DateTimeField(db_comment="更新时间", auto_now=True)

    class Meta:
        managed = False
        db_table = "role_menu"
        db_table_comment = "角色菜单关系表"

    def __str__(self):
        return f"<RoleMenu: {self.role_id} - {self.menu_id}>"


class UserRole(models.Model):
    user_id = models.IntegerField(db_comment="用户ID")
    role_id = models.IntegerField(db_comment="角色ID")

    created_at = models.DateTimeField(db_comment="创建时间", auto_now_add=True)

    class Meta:
        managed = False
        db_table = "user_role"
        db_table_comment = "用户角色关系表"

    def __str__(self):
        return f"<UserRole: {self.user_id} - {self.role_id}>"
