from collections import defaultdict

from income import const
from income.operation_log.utils import OperationLogManager
from income.operation_log.utils import get_client_ip

from .models import Menu
from .models import Role
from .models import RoleMenu
from .models import User
from .models import UserDepartment
from .models import UserRole


def get_menu_info(role_id: int):
    """根据role_id获取对应的菜单权限

    :param role_id: 角色ID
    :return:
    """
    # 获取当前 role 对应的所有 menu_id - operation
    role_menu_list = RoleMenu.objects.filter(role_id=role_id).values(
        "menu_id",
        "operation",
    )
    menus_info = Menu.objects.filter(
        id__in=[role_menu["menu_id"] for role_menu in role_menu_list],
    ).values_list("id", "menu_name")

    menu_mapping = dict(menus_info)

    for role_menu in role_menu_list:
        role_menu["menu_name"] = menu_mapping[role_menu["menu_id"]]

    return role_menu_list


def get_profile_menu_info(role_ids: list, *, super_admin: bool = False):
    """根据role_id获取对应的菜单权限

    :param role_ids: 角色ID
    :param super_admin: 是否为超级管理员
    :return:
    """
    fields = ["id", "menu_name", "icon", "router", "parent_id", "identify"]
    # 如果用户为超级管理员
    if super_admin:
        menus_info = Menu.objects.exclude(menu_name="ALL").values(*fields)
        for item in menus_info:
            item["operation"] = item["parent_id"] is not None
            item["menu_id"] = item.pop("id")
        return menus_info

    # 获取当前 role 对应的所有 menu_id - operation
    role_menu_list = RoleMenu.objects.filter(role_id__in=role_ids).values(
        "menu_id",
        "operation",
    )

    # 处理重复的{menu_id: operation}项
    result_dict = {}
    for item in role_menu_list:
        menu_id = item["menu_id"]
        # 如果 menu_id 不在结果中,或者当前项的operation为True且已有项的operation不是True
        if menu_id not in result_dict or (
            item["operation"] is True and result_dict[menu_id]["operation"] is not True
        ):
            result_dict[menu_id] = item

    merge_result = result_dict.values()

    menus_info = Menu.objects.filter(
        id__in=[role_menu["menu_id"] for role_menu in merge_result],
    ).values(*fields)

    menu_mapping = {menu.pop("id"): menu for menu in menus_info}

    for role_menu in merge_result:
        role_menu.update(**menu_mapping[role_menu["menu_id"]])

    return merge_result


def add_extra_field(query_data_list: list):
    """添加部门名称

    :param query_data_list:
    :return:
    """
    user_ids = []
    department_ids = []
    for query_data in query_data_list:
        user_ids.append(query_data["id"])
        department_ids.append(query_data["department_id"])
    department_queryset = UserDepartment.objects.filter(
        pk__in=set(department_ids),
    ).values_list("id", "department_name")
    department_map = dict(department_queryset)

    ur_list = UserRole.objects.filter(user_id__in=user_ids).values("user_id", "role_id")
    ur_map = defaultdict(list)
    for ur in ur_list:
        ur_map[ur["user_id"]].append(ur["role_id"])

    for query_data in query_data_list:
        query_data["department_name"] = department_map.get(
            query_data.pop("department_id"),
        )
        query_data["role_ids"] = ur_map[query_data["id"]]
    return query_data_list


def user_update_operation_log(
    request,
    instance: User,
    old_instance: User,
    old_user_roles: list[int],
):
    """记录用户操作日志

    Args:
        request: HTTP请求对象
        instance: 用户实例
        old_instance: 修改前的实例(更新操作时需要)
        old_user_roles: 修改前的用户的角色列表
    """
    operation = OperationLogManager()

    changes = operation.compare_model_fields(
        old_instance,
        instance,
        operation.get_field_comment_mapping(User),
    )

    # 获取用户的角色信息是否有变动, 有变动时记录变动信息
    old_role_ids = set(old_user_roles)
    new_role_ids = set(instance.user_roles)
    if old_role_ids != new_role_ids:
        # 获取角色名称,用','进行拼接
        role_names = ",".join(
            Role.objects.filter(id__in=new_role_ids).values_list(
                "role_name",
                flat=True,
            ),
        )
        old_role_names = ",".join(
            Role.objects.filter(id__in=old_role_ids).values_list(
                "role_name",
                flat=True,
            ),
        )
        changes.append(
            {
                "field_name": "角色",
                "old_value": old_role_names,
                "new_value": role_names,
            },
        )
    operation_content = changes if changes else None

    operation.create_operation_log(
        operator=request.user.username,
        operation_type=const.OperationType.UPDATE,
        table_name=const.OperationLogTable.USER,
        table_id=instance.id,
        operation_content=operation_content,
        ip_address=get_client_ip(request),
    )
