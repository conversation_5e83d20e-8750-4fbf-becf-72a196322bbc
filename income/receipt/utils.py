from django.db.models import DecimalField
from django.db.models import F
from django.db.models import Value
from django.db.models.functions import Coalesce

from income.invoice.models import IncomeInvoice

from .models import IncomeBankStatement


def add_extra_field(query_data_list: list):
    """添加额外字段

    :param query_data_list:
    :return:
    """
    for query_data in query_data_list:
        query_data["created_at"] = query_data["created_at"].strftime("%s")
    return query_data_list


def calc_unconfirmed_amount(bank_statement_id: int):
    """计算银行流水中未确认金额

    :param bank_statement_id: 银行流水ID
    :return:
    """
    # amount - confirmed_amount - confirming_amount
    return (
        IncomeBankStatement.objects.annotate(
            unconfirmed_amount=Coalesce(
                F("receive_amount") - F("confirmed_amount") - F("confirming_amount"),
                Value(0),
                output_field=DecimalField(
                    max_digits=10,
                    decimal_places=2,
                ),
            ),
        )
        .values_list("unconfirmed_amount", flat=True)
        .get(pk=bank_statement_id)
    )


def calc_invoice_remaining_amount(invoice_no: str):
    """计算发票的剩余金额

    :param invoice_no: 发票号
    :return:
    """
    # 发票的剩余金额计算公式: current_amount - write_off_amount  # noqa: ERA001
    return (
        IncomeInvoice.objects.annotate(
            remaining_amount=Coalesce(
                F("current_amount") - F("write_off_amount"),
                Value(0),
                output_field=DecimalField(
                    max_digits=10,
                    decimal_places=2,
                ),
            ),
        )
        .values_list("remaining_amount", flat=True)
        .get(invoice_no=invoice_no)
    )
