from django.db import models

from income import const


class IncomeBankStatement(models.Model):
    """对账信息表"""

    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        db_comment="交易金额(精确到分)",
    )
    currency_type = models.Char<PERSON>ield(
        max_length=10,
        blank=True,
        null=True,
        db_comment="币种",
    )
    receive_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        db_comment="收款金额",
    )
    receive_currency_type = models.CharField(
        max_length=10,
        blank=True,
        null=True,
        db_comment="收款币种",
    )
    exchange_rate = models.DecimalField(
        max_digits=18,
        decimal_places=10,
        blank=True,
        null=True,
        db_comment="汇率,计算方式 receive_amount/amount,保留10位小数",
    )
    statement_no = models.CharField(unique=True, max_length=50, db_comment="对账单编号")
    statement_date = models.DateField(db_comment="对账单日期")
    payment_name = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        db_comment="付款方名称",
    )
    payment_bank_no = models.CharField(
        max_length=30,
        blank=True,
        null=True,
        db_comment="付款方银行账号",
    )
    payment_bank_name = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        db_comment="付款方银行名称",
    )
    receive_bank_name = models.CharField(
        max_length=40,
        blank=True,
        null=True,
        db_comment="收款银行名称",
    )
    receive_bank_no = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        db_comment="收款方银行账号",
    )
    confirming_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        db_comment="待确认金额",
    )
    confirmed_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        db_comment="已确认金额",
    )
    customer_num = models.CharField(
        max_length=40,
        blank=True,
        null=True,
        db_comment="客户编号",
    )
    customer_name = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        db_comment="客户名称",
    )
    state = models.CharField(max_length=20, db_comment="对账状态")
    description = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        db_comment="备注",
    )

    created_at = models.DateTimeField(db_comment="创建时间", auto_now_add=True)
    updated_at = models.DateTimeField(db_comment="更新时间", auto_now=True)

    class Meta:
        managed = False
        db_table = "income_bank_statement"
        db_table_comment = "对账信息表"
        ordering = ("-created_at",)

    def __str__(self):
        return f"<IncomeBankStatement: {self.statement_no}>"


class IncomeReceiveStatement(models.Model):
    bank_statement_id = models.IntegerField(
        blank=True,
        null=True,
        db_comment="银行流水id",
    )
    bank_statement_no = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        db_comment="银行流水号",
    )

    created_at = models.DateTimeField(db_comment="创建时间", auto_now_add=True)
    updated_at = models.DateTimeField(db_comment="更新时间", auto_now=True)

    class Meta:
        managed = False
        db_table = "income_receive_statement"
        db_table_comment = "收入确认表"
        ordering = ("-created_at",)

    def __str__(self):
        return f"<IncomeReceiveStatement: {self.bank_statement_no}>"


class IncomeReceiveStatementTmp(models.Model):
    """收入确认表临时表"""

    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        db_comment="认款/核销金额",
    )
    sub_order_no = models.CharField(
        max_length=50,
        db_comment="收款订单号: total_num",
    )
    receive_type = models.CharField(
        max_length=10,
        choices=const.ReceiveType.choices,
        default=const.ReceiveType.WRITE_OFF,
        db_comment="核销/收款",
    )
    remark = models.CharField(max_length=512, blank=True, null=True)
    group_approve_state = models.IntegerField(
        blank=True,
        null=True,
        choices=const.GroupApproveState.choices,
        default=const.GroupApproveState.INIT,
    )

    receive_statement_id = models.IntegerField(
        blank=True,
        null=True,
        db_comment="收入确认表id",
    )
    income_charge_detail_id = models.IntegerField(db_comment="权责id")
    bank_statement_id = models.IntegerField(db_comment="银行流水id")

    created_at = models.DateTimeField(db_comment="创建时间", auto_now_add=True)
    updated_at = models.DateTimeField(db_comment="更新时间", auto_now=True)

    class Meta:
        managed = False
        db_table = "income_receive_statement_tmp"
        db_table_comment = "收入确认表临时表"
        ordering = ("-created_at",)

    def __str__(self):
        return f"<IncomeReceiveStatementTmp: {self.id}>"


class IncomeInvoiceReceiveRel(models.Model):
    """发票收款关联关系表"""

    bank_statement_id = models.IntegerField(
        blank=True,
        null=True,
        db_comment="银行流水id",
    )
    invoice_detail_id = models.IntegerField(
        blank=True,
        null=True,
        db_comment="发票明细id",
    )
    created_at = models.DateTimeField(db_comment="创建时间", auto_now_add=True)

    class Meta:
        managed = False
        db_table = "income_invoice_receive_rel"
        db_table_comment = (
            "发票收款关联关系表,关联income_bank_statement和income_invoice_detail表"
        )
        ordering = ("-created_at",)

    def __str__(self):
        return f"<IncomeInvoiceReceiveRel: {self.id}>"
