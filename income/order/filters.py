from django.utils.encoding import force_str
from rest_framework.filters import BaseFilterBackend

from income.contract.models import ContractInfo
from income.contrib.drf.filters import BaseFilter
from income.contrib.drf.utils import get_value
from income.contrib.drf.utils import make_markdown_table


class ContractLegalNumberFilter(BaseFilter):
    """合同法务编号过滤器

    search_param: 搜索值
    """

    search_param = "contract_legal_num"
    description = "合同法务编号搜索"

    def filter_queryset(self, request, queryset, view):
        contract_legal_num = get_value(request, self.search_param)
        if contract_legal_num:
            contract_nums = ContractInfo.objects.filter(
                contract_legal_num=contract_legal_num,
            ).values_list("contract_num", flat=True)
            queryset = queryset.filter(contract_num__in=contract_nums)
        return queryset


class CustomerNumFilter(BaseFilter):
    """客户编号过滤器

    search_param: 搜索值
    """

    search_param = "customer_num"
    description = "客户编号搜索"

    def filter_queryset(self, request, queryset, view):
        customer_num = get_value(request, self.search_param)
        if customer_num:
            queryset = queryset.filter(customer_num=customer_num)
        return queryset


class OrderStatusLogStatusTypeFilter(BaseFilterBackend):
    """
    status_param: 状态参数
    status_choices: 状态的枚举
    """

    status_param = "status_type"
    status_choices = "status_type_choices"

    def filter_queryset(self, request, queryset, view):
        status = get_value(request, self.status_param)
        status_choices = getattr(view, self.status_choices)
        if status in status_choices.values:
            queryset = queryset.filter(status_type=status)
        return queryset

    def get_schema_operation_parameters(self, view):
        status_choices = getattr(view, self.status_choices)
        return [
            {
                "name": self.status_param,
                "required": False,
                "in": "query",
                "description": force_str(make_markdown_table(status_choices.choices)),
                "schema": {
                    "type": "enum",
                    "enum": status_choices.values,
                },
            },
        ]
