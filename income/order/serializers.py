from datetime import datetime

from django.db import transaction
from django.db.models import Max
from django.utils import timezone
from rest_framework import serializers

from income import const
from income import message
from income.customer.models import IncomeAccountSeq
from income.fee.models import IncomeFeePackage
from income.utils.file_handler import validate_and_save_file

from .models import IncomeOrderFile
from .models import IncomeOrderInfo
from .models import IncomeOrderStatusLog
from .utils import log_order_status_change


class OrderInfoSerializer(serializers.ModelSerializer):
    product_files = serializers.ListField(
        child=serializers.FileField(allow_empty_file=False, use_url=False),
        write_only=True,
        required=False,
        help_text="产品文件",
    )
    finished_files = serializers.ListField(
        child=serializers.FileField(allow_empty_file=False, use_url=False),
        write_only=True,
        required=False,
        help_text="完工文件",
    )

    class Meta:
        model = IncomeOrderInfo
        exclude = ("order_num", "charge_order_num", "updated_at")
        read_only_fields = const.ORDER_READ_ONLY_FIELDS
        write_only_fields = ("sub_order_num",)

    @staticmethod
    def generate_order_num():
        current_time = datetime.now().strftime("%Y%m")
        max_order_num = IncomeOrderInfo.objects.filter(
            order_num__startswith=current_time,
        ).aggregate(number_max=Max("order_num"))
        max_num = max_order_num.get("number_max")
        return (
            f"{current_time}{(int(max_num[6:]) + 1):0>6}"
            if max_num
            else f"{current_time}000001"
        )

    @staticmethod
    def generate_charge_order_num(
        business_product_type: str,
        order_num: str,
        sub_order_num: str,
    ):
        """生成递增的charge_order_num

        :param business_product_type: 业务产品类型
        :param order_num: 订单编号
        :param sub_order_num: 子订单编号
        :return: str
        """
        prefix = "BG"
        max_charge_order_num = IncomeOrderInfo.objects.filter(
            business_product_type=business_product_type,
            order_num=order_num,
            sub_order_num=sub_order_num,
            charge_order_num__startswith=prefix,
        ).aggregate(number_max=Max("charge_order_num"))
        max_num = max_charge_order_num.get("number_max")
        return f"{prefix}{int(max_num[2:]) + 1}" if max_num else f"{prefix}0"

    def custom_validate_total_num(self, total_num: str):
        """校验合成编号是否存在"""

        if self.Meta.model.objects.filter(total_num=total_num).exists():
            raise serializers.ValidationError(
                {"total_num": message.ORDER_TOTAL_NUM_EXISTS},
            )

    def validate_income_fee_package_id(self, value):
        """校验套餐是否存在"""

        if value and not IncomeFeePackage.objects.filter(id=value).exists():
            raise serializers.ValidationError(message.FEE_PACKAGE_NOT_FOUND)
        return value

    def validate_product_files(self, value):
        """校验产品文件"""
        file_info_list = []
        for file in value:
            result = validate_and_save_file(
                file=file,
                category="order_product",  # 使用预定义的类别
                allowed_types=["pdf", "word", "excel", "image"],  # 限制文件类型
                max_size=20 * 1024 * 1024,  # 20MB限制
                check_duplicates=False,  # 重复检查
                check_security=True,  # 安全检查
            )
            if not result["success"]:
                # 如果有警告,记录日志
                if result.get("warnings"):
                    import logging

                    logger = logging.getLogger("file_handler")
                    for warning in result["warnings"]:
                        logger.warning(f"文件上传警告: {warning}")  # noqa: G004
                raise serializers.ValidationError(result["errors"])
            file_info_list.append(result["file_info"])
        self.product_file_info_list = file_info_list
        return value

    def validate_finished_files(self, value):
        """校验完工文件"""
        file_info_list = []
        for file in value:
            result = validate_and_save_file(
                file=file,
                category="order_finished",  # 使用预定义的类别
                allowed_types=["pdf", "word", "excel", "image"],  # 限制文件类型
                max_size=20 * 1024 * 1024,  # 20MB限制
                check_duplicates=False,  # 重复检查
                check_security=True,  # 安全检查
            )
            if not result["success"]:
                # 如果有警告,记录日志
                if result.get("warnings"):
                    import logging

                    logger = logging.getLogger("file_handler")
                    for warning in result["warnings"]:
                        logger.warning(f"文件上传警告: {warning}")  # noqa: G004
                raise serializers.ValidationError(result["errors"])
            file_info_list.append(result["file_info"])
        self.finished_file_info_list = file_info_list
        return value

    def validate(self, attrs):
        # 判断如果是修改,
        if self.instance:
            # 并且子订单编号进行过变动
            if self.instance.sub_order_num != attrs["sub_order_num"]:
                # 仅修改charge_order_num
                charge_order_num = self.generate_charge_order_num(
                    attrs["business_product_type"],
                    self.instance.order_num,
                    attrs["sub_order_num"],
                )
                attrs["charge_order_num"] = charge_order_num
                # 变更合成编号
                total_num = f"""{attrs["business_product_type"]}-{self.instance.order_num}-{attrs["sub_order_num"]}-{charge_order_num}"""  # noqa: E501
                self.custom_validate_total_num(total_num)
                attrs["total_num"] = total_num
            return attrs
        # 新建操作时
        order_num = self.generate_order_num()
        attrs["order_num"] = order_num
        charge_order_num = self.generate_charge_order_num(
            attrs["business_product_type"],
            order_num,
            attrs["sub_order_num"],
        )
        attrs["charge_order_num"] = charge_order_num
        total_num = f"""{attrs["business_product_type"]}-{order_num}-{attrs["sub_order_num"]}-{charge_order_num}"""  # noqa: E501
        self.custom_validate_total_num(total_num)
        attrs["total_num"] = total_num
        return attrs

    @staticmethod
    def bulk_create_order_file(
        order_instance,
        create_user,
        file_info_list,
        file_class,
    ):
        """批量创建订单文件

        :param order_instance: 订单实例
        :param create_user: 创建者
        :param file_info_list: 文件信息列表
        :param file_class: 文件类型
        :return:
        """
        IncomeOrderFile.objects.bulk_create(
            [
                IncomeOrderFile(
                    order_id=order_instance.id,
                    file_name=file_info["original_name"],
                    save_file_name=file_info["saved_name"],
                    file_path=file_info["relative_path"],
                    file_class=file_class,
                    create_user=create_user,
                )
                for file_info in file_info_list
            ],
            batch_size=50,
        )

    def create(self, validated_data):
        create_user = self.context["request"].user.username
        validated_data["create_user"] = create_user
        validated_data["service_status"] = const.OrderServiceStatus.DRAFT
        validated_data["bill_status"] = const.OrderBillStatus.INCOMPLETE
        validated_data.pop("product_files")
        validated_data.pop("finished_files")
        with transaction.atomic():
            order_instance = self.Meta.model.objects.create(**validated_data)
            self.bulk_create_order_file(
                order_instance,
                create_user,
                self.product_file_info_list,
                const.FileClass.PRODUCT,
            )
            self.bulk_create_order_file(
                order_instance,
                create_user,
                self.finished_file_info_list,
                const.FileClass.FINISHED,
            )
        return order_instance

    def update(self, instance, validated_data):
        request = self.context["request"]
        create_user = request.user.username
        validated_data["create_user"] = create_user
        product_file_info_list = validated_data.pop("product_files", None)
        finished_file_info_list = validated_data.pop("finished_files", None)
        with transaction.atomic():
            for k, v in validated_data.items():
                setattr(instance, k, v)
            instance.save()
            if product_file_info_list:
                self.bulk_create_order_file(
                    instance,
                    create_user,
                    self.product_file_info_list,
                    const.FileClass.PRODUCT,
                )
            if finished_file_info_list:
                self.bulk_create_order_file(
                    instance,
                    create_user,
                    self.finished_file_info_list,
                    const.FileClass.FINISHED,
                )
        return instance


class OrderChangeServiceStatusSerializer(serializers.ModelSerializer):
    """更新服务状态"""

    service_status = serializers.ChoiceField(
        choices=const.OrderServiceStatus.choices,
        help_text="变更的服务状态",
    )

    class Meta:
        model = IncomeOrderInfo
        fields = (
            "service_status",
            "finished_remark",
            "order_remark",
            "new_build_start_time",
            "job_status",
            "product_scheme",
            "remove_required_finished_date",
            "remove_build_finished_time",
        )

    def validate(self, attrs):
        # 服务状态变更为拆机回退和变更拆机回退时,需服务状态为服务中和账务状态为计费中
        if attrs["service_status"] in [
            const.OrderServiceStatus.DECOM_ROLLBACK,
            const.OrderServiceStatus.CHG_DECOM_ROLLBACK,
        ] and (
            self.instance.service_status != const.OrderServiceStatus.IN_SERVICE
            or self.instance.bill_status != const.OrderBillStatus.IN_PROGRESS
        ):
            raise serializers.ValidationError(
                {"service_status": message.ORDER_SERVICE_STATUS_NOT_DECOM_ROLLBACK},
            )
        return attrs

    def update(self, instance, validated_data):
        # 服务状态变更为 变更服务终止/服务终止 时,记录remove_build_support_time
        # 变更为 变更拆机维护信息复核/拆机维护信息复核 时,记录remove_build_finished_time
        status_to_time_field = {
            const.OrderServiceStatus.DECOM_ROLLBACK: "remove_build_start_time",
            const.OrderServiceStatus.CHG_DECOM_ROLLBACK: "remove_build_start_time",
            const.OrderServiceStatus.CHG_DECOM_MAINTENANCE_REVIEW: "remove_build_support_time",  # noqa: E501
            const.OrderServiceStatus.DECOM_MAINTENANCE_REVIEW: "remove_build_support_time",  # noqa: E501
        }
        new_status = validated_data.get("service_status")
        if new_status in status_to_time_field:
            validated_data[status_to_time_field[new_status]] = timezone.now()
        return super().update(instance, validated_data)

    def save(self, **kwargs):
        """保存时记录状态变更日志"""
        old_status = self.instance.service_status
        new_status = self.validated_data.get("service_status")

        # 保存实例
        instance = super().save(**kwargs)

        # 记录状态变更日志
        if old_status != new_status:
            request = self.context.get("request")
            log_order_status_change(
                order_instance=instance,
                status_type=const.OrderStatusLogType.SERVICE_STATUS,
                old_status=old_status,
                new_status=new_status,
                operator=request.user.username,
            )

        return instance


class OrderChangeBillStatusSerializer(serializers.ModelSerializer):
    """更新计费状态"""

    bill_status = serializers.ChoiceField(
        choices=const.OrderBillStatus.choices,
        help_text="变更的计费状态",
    )

    class Meta:
        model = IncomeOrderInfo
        fields = (
            "bill_status",
            "account_seq",
            "reality_bill_start_date",
            "order_start_year",
            "order_remark",
            "finished_remark",
            "reality_bill_end_date",
        )

    def update(self, instance, validated_data):
        # 当状态到达 新装账务确认 时,记录new_build_bill_time
        # 到达 计费中 时,记录new_build_charge_time
        # 到达 变更拆机账务确认/拆机账务确认 时,记录remove_build_bill_time
        # 到达 变更计费终止/计费终止 时,记录remove_build_charge_time
        status_to_time_field = {
            const.OrderBillStatus.FINANCIAL_CONFIRMED: "new_build_bill_time",
            const.OrderBillStatus.IN_PROGRESS: "new_build_charge_time",
            const.OrderBillStatus.DECOM_FINANCIAL_CONFIRMED: "remove_build_bill_time",
            const.OrderBillStatus.CHG_DECOM_FINANCIAL_CONFIRMED: "remove_build_bill_time",  # noqa: E501
            const.OrderBillStatus.TERMINATED: "remove_build_charge_time",
            const.OrderBillStatus.CHG_TERMINATED: "remove_build_charge_time",
        }
        new_status = validated_data.get("bill_status")
        if new_status in status_to_time_field:
            validated_data[status_to_time_field[new_status]] = timezone.now()
        return super().update(instance, validated_data)

    def save(self, **kwargs):
        """保存时, 如果状态变更为计费中, 根据account_seq更新tax_rate字段"""
        old_status = self.instance.bill_status
        new_status = self.validated_data.get("bill_status")

        # 保存实例
        instance = super().save(**kwargs)

        # 记录状态变更日志
        if old_status != new_status:
            request = self.context.get("request")
            log_order_status_change(
                order_instance=instance,
                status_type=const.OrderStatusLogType.BILL_STATUS,
                old_status=old_status,
                new_status=new_status,
                operator=request.user.username,
            )

        # 如果计费状态变更为"计费中", 且有account_seq, 则更新tax_rate
        if new_status == const.OrderBillStatus.IN_PROGRESS and instance.account_seq:
            try:
                # 根据account_seq查找对应的税率
                tax = IncomeAccountSeq.objects.values_list("tax", flat=True).get(
                    account_seq=instance.account_seq,
                )
                # 更新订单的tax_rate字段
                instance.tax_rate = tax
                instance.save(update_fields=["tax_rate", "updated_at"])
            except IncomeAccountSeq.DoesNotExist:
                pass

        return instance


class ChangeOrderSerializer(serializers.ModelSerializer):
    """变更订单信息"""

    class Meta:
        model = IncomeOrderInfo
        exclude = ("updated_at",)
        read_only_fields = (
            *const.ORDER_READ_ONLY_FIELDS,
            *(
                "sub_order_num",
                "charge_order_num",
                "order_num",
            ),
        )

    def validate(self, attrs):
        # 从context中获取parent_order
        parent_order = self.context.get("parent_order")

        # 变更订单时将charge_order_num向上加1, BG0->BG1
        # 保留原订单的order_num和sub_order_num
        attrs["order_num"] = parent_order.order_num
        attrs["sub_order_num"] = parent_order.sub_order_num

        # 从BG0获取数字部分, 转为整数后加1
        current_charge_num = parent_order.charge_order_num
        prefix = "BG"
        num_part = int(current_charge_num[2:])
        new_charge_order_num = f"{prefix}{num_part + 1}"
        attrs["charge_order_num"] = new_charge_order_num

        # 更新total_num
        total_num = f"""{
            attrs.get("business_product_type", parent_order.business_product_type)
        }-{parent_order.order_num}-{parent_order.sub_order_num}-{
            new_charge_order_num
        }"""
        self.custom_validate_total_num(total_num)
        attrs["total_num"] = total_num

        attrs["service_status"] = const.OrderServiceStatus.DRAFT
        attrs["bill_status"] = const.OrderBillStatus.INCOMPLETE
        attrs["service_type"] = const.ServiceType.CHANGE
        # 设置原订单为前序订单
        attrs["pre_order_total_num"] = parent_order.total_num
        return attrs

    def custom_validate_total_num(self, total_num: str):
        """校验合成编号是否存在"""
        if self.Meta.model.objects.filter(total_num=total_num).exists():
            raise serializers.ValidationError(
                {"total_num": message.ORDER_TOTAL_NUM_EXISTS},
            )

    def create(self, validated_data):
        create_user = self.context["request"].user.username
        validated_data["create_user"] = create_user
        return self.Meta.model.objects.create(**validated_data)


class NewSubOrderSerializer(serializers.ModelSerializer):
    """新增子订单"""

    class Meta:
        model = IncomeOrderInfo
        exclude = ("updated_at",)
        read_only_fields = (
            *const.ORDER_READ_ONLY_FIELDS,
            *(
                "charge_order_num",
                "order_num",
            ),
        )

    def validate(self, attrs):
        # 从context中获取parent_order
        parent_order = self.context.get("parent_order")
        # 保留原订单的order_num
        attrs["order_num"] = parent_order.order_num

        # charge_order_num设置为默认值BG0
        attrs["charge_order_num"] = "BG0"

        # 更新total_num
        total_num = f"""{
            attrs.get("business_product_type", parent_order.business_product_type)
        }-{parent_order.order_num}-{attrs["sub_order_num"]}-BG0"""
        self.custom_validate_total_num(total_num)
        attrs["total_num"] = total_num

        # 设置默认状态
        attrs["service_status"] = const.OrderServiceStatus.DRAFT
        attrs["bill_status"] = const.OrderBillStatus.INCOMPLETE

        # 设置服务类型和原订单为前序订单
        attrs["service_type"] = const.ServiceType.NEW
        attrs["pre_order_total_num"] = parent_order.total_num

        return attrs

    def custom_validate_total_num(self, total_num: str):
        """校验合成编号是否存在"""
        if self.Meta.model.objects.filter(total_num=total_num).exists():
            raise serializers.ValidationError(
                {"total_num": message.ORDER_TOTAL_NUM_EXISTS},
            )

    def create(self, validated_data):
        create_user = self.context["request"].user.username
        validated_data["create_user"] = create_user
        # 创建新的子订单记录
        return self.Meta.model.objects.create(**validated_data)


class RenewalOrderSerializer(serializers.ModelSerializer):
    """续约订单序列化器"""

    class Meta:
        model = IncomeOrderInfo
        exclude = ("updated_at", "order_num", "charge_order_num")
        read_only_fields = (*const.ORDER_READ_ONLY_FIELDS,)
        write_only_fields = ("sub_order_num",)

    def validate(self, attrs):
        # 从context中获取parent_order
        parent_order = self.context.get("parent_order")

        # 续约订单需要重新生成order_num
        order_num = OrderInfoSerializer.generate_order_num()
        attrs["order_num"] = order_num

        # 生成charge_order_num
        business_product_type = attrs["business_product_type"]
        charge_order_num = OrderInfoSerializer.generate_charge_order_num(
            business_product_type,
            order_num,
            attrs["sub_order_num"],
        )
        attrs["charge_order_num"] = charge_order_num

        # 拼接total_num
        total_num = f"""{business_product_type}-{order_num}-{attrs["sub_order_num"]}-{charge_order_num}"""  # noqa: E501
        self.custom_validate_total_num(total_num)
        attrs["total_num"] = total_num

        # 设置默认状态
        attrs["service_status"] = const.OrderServiceStatus.DRAFT
        attrs["bill_status"] = const.OrderBillStatus.INCOMPLETE
        # 设置服务类型为续约,并设置前序订单
        attrs["service_type"] = const.ServiceType.RENEWAL
        attrs["pre_order_total_num"] = parent_order.total_num

        return attrs

    def custom_validate_total_num(self, total_num: str):
        """校验合成编号是否存在"""
        if self.Meta.model.objects.filter(total_num=total_num).exists():
            raise serializers.ValidationError(
                {"total_num": message.ORDER_TOTAL_NUM_EXISTS},
            )

    def create(self, validated_data):
        create_user = self.context["request"].user.username
        validated_data["create_user"] = create_user
        # 创建续约订单记录
        return self.Meta.model.objects.create(**validated_data)


class OrderFileSerializer(serializers.ModelSerializer):
    """订单文件序列化器"""

    class Meta:
        model = IncomeOrderFile
        fields = (
            "id",
            "file_name",
            "file_class",
        )


class OrderStatusLogSerializer(serializers.ModelSerializer):
    """订单状态日志序列化器"""

    class Meta:
        model = IncomeOrderStatusLog
        fields = (
            "id",
            "order_id",
            "total_num",
            "status_type",
            "old_status",
            "new_status",
            "remark",
            "is_rollback",
            "operator",
            "created_at",
        )
