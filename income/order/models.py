from django.db import models

from income import const


class IncomeOrderInfo(models.Model):
    """收入订单信息"""

    order_type = models.CharField(
        max_length=20,
        choices=const.OrderType.choices,
        db_comment="订单类型",
    )
    order_class = models.IntegerField(
        blank=True,
        null=True,
        choices=const.OrderClass.choices,
        db_comment="订单种类",
    )
    service_status = models.CharField(
        max_length=50,
        choices=const.OrderServiceStatus.choices,
        db_comment="订单服务状态",
    )
    bill_status = models.CharField(
        max_length=50,
        choices=const.OrderBillStatus.choices,
        db_comment="订单计费状态",
    )
    job_status = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        choices=const.JobStatus.choices,
        db_comment="派工状态",
    )
    order_start_year = models.CharField(
        max_length=10,
        db_comment="订单起始年",
        blank=True,
        null=True,
    )
    order_remark = models.TextField(blank=True, null=True, db_comment="订单备注")
    business_product_type = models.CharField(
        max_length=20,
        db_comment="业务产品类型",
    )

    order_num = models.CharField(
        max_length=50,
        db_comment="业务产品字母-4位年2位月6位递增数字",
    )
    sub_order_num = models.CharField(max_length=1, db_comment="A-Z")
    charge_order_num = models.CharField(
        max_length=10,
        db_comment="自动递增数字, 起始为BG0",
    )
    total_num = models.CharField(
        max_length=50,
        db_comment="合成编号:订单编号-子订单序号-BG变更序号",
        unique=True,
        error_messages={
            "unique": "合成编号重复,请重新输入相关字段",
        },
    )

    service_type = models.CharField(
        max_length=10,
        choices=const.ServiceType.choices,
        db_comment="服务类型",
    )
    pre_order_total_num = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        db_comment="前序订单合成编号",
    )
    # static_data表type为income_type
    income_type = models.CharField(
        max_length=40,
        db_comment="收入分类",
    )
    product_main_category = models.CharField(
        max_length=40,
        blank=True,
        null=True,
        db_comment="产品主类",
    )
    product_sub_category = models.CharField(
        max_length=40,
        db_collation="utf8mb4_bin",
        blank=True,
        null=True,
        db_comment="产品次类",
    )
    product_scheme = models.TextField(blank=True, null=True, db_comment="产品开通方案")
    product_after_sale = models.TextField(
        blank=True,
        null=True,
        db_comment="售后服务要求",
    )
    pay_cycle = models.CharField(
        max_length=10,
        blank=True,
        null=True,
        choices=const.PayCycle.choices,
        db_comment="付费周期",
    )
    pay_type = models.CharField(
        max_length=10,
        blank=True,
        null=True,
        choices=const.PayType.choices,
        db_comment="付费方式",
    )
    account_seq = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        db_comment="分账序号",
    )

    a_info = models.CharField(
        max_length=500,
        blank=True,
        null=True,
        db_comment="a端信息",
    )
    a_address = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        db_comment="a端地址",
    )
    z_info = models.CharField(
        max_length=500,
        blank=True,
        null=True,
        db_comment="z端信息",
    )
    z_address = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        db_comment="z段地址",
    )

    partner_name = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        db_comment="合作商名称",
    )
    partner_po_num = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        db_comment="合作商PO编号",
    )
    new_required_finished_date = models.DateField(
        blank=True,
        null=True,
        db_comment="新装要求完工日",
    )
    reality_bill_start_date = models.DateField(
        blank=True,
        null=True,
        db_comment="实际计费始日",
    )
    new_build_start_time = models.DateTimeField(
        blank=True,
        null=True,
        db_comment="新装实施开始时间(实际开始实施时间)",
    )
    new_build_finished_time = models.DateTimeField(
        blank=True,
        null=True,
        db_comment="新装实施报完工时间(实施报完工)",
    )
    new_build_bill_time = models.DateTimeField(
        blank=True,
        null=True,
        db_comment="新装计费报完工时间(收入报完工)",
    )
    new_build_charge_time = models.DateTimeField(
        blank=True,
        null=True,
        db_comment="新装账务复核时间(账务确认时间)",
    )
    new_build_support_time = models.DateTimeField(
        blank=True,
        null=True,
        db_comment="新装维护确认时间(客服确认时间)",
    )
    remove_required_finished_date = models.DateField(
        blank=True,
        null=True,
        db_comment="拆机要求完工日",
    )
    reality_bill_end_date = models.DateField(
        blank=True,
        null=True,
        db_comment="实际计费终日",
    )
    remove_build_start_time = models.DateTimeField(
        blank=True,
        null=True,
        db_comment="拆机实施开始时间",
    )
    remove_build_finished_time = models.DateTimeField(
        blank=True,
        null=True,
        db_comment="拆机实施报完工时间",
    )
    remove_build_bill_time = models.DateTimeField(
        blank=True,
        null=True,
        db_comment="拆机计费报完工时间",
    )
    remove_build_charge_time = models.DateTimeField(
        blank=True,
        null=True,
        db_comment="拆机账务复核时间",
    )
    remove_build_support_time = models.DateTimeField(
        blank=True,
        null=True,
        db_comment="拆机维护确认时间",
    )
    finished_remark = models.TextField(blank=True, null=True, db_comment="完工备注")
    group_approve_state = models.CharField(
        max_length=10,
        blank=True,
        null=True,
        db_comment="集团审批状态",
    )
    income_fee_package_id = models.IntegerField(
        db_comment="套餐",
        blank=True,
        null=True,
    )
    once_fee = models.IntegerField(blank=True, null=True, db_comment="一次性费用")
    cycle_fee = models.IntegerField(blank=True, null=True, db_comment="周期费用")
    tax_rate = models.IntegerField(blank=True, null=True, db_comment="税率")
    tax_type = models.CharField(max_length=50, db_comment="税率类型")
    currency_type = models.CharField(max_length=20, db_comment="币种")
    charge_explain = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        db_comment="资费说明",
    )
    charge_remark = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        db_comment="资费备注",
    )

    contract_num = models.CharField(max_length=50, db_comment="合同编号")
    customer_num = models.CharField(max_length=50, db_comment="客户编号")
    create_user = models.CharField(max_length=50, db_comment="创建者")

    created_at = models.DateTimeField(db_comment="创建时间", auto_now_add=True)
    updated_at = models.DateTimeField(db_comment="更新时间", auto_now=True)

    class Meta:
        managed = False
        db_table = "income_order_info"
        ordering = ["-created_at"]

    def __str__(self):
        return f"<IncomeOrderInfo: {self.total_num}>"


class IncomeOrderFile(models.Model):
    """订单文件"""

    order_id = models.IntegerField(db_comment="订单id")
    file_name = models.CharField(
        max_length=256,
        db_comment="文件名称",
    )
    save_file_name = models.CharField(
        max_length=100,
        db_comment="保存的文件名称",
    )
    s3_upload = models.IntegerField(
        default=0,
        db_comment="是否已上传上s3 0:未上传; 1:已上传",
    )
    file_path = models.CharField(
        max_length=256,
        db_comment="文件路径",
    )
    s3_file_path = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        db_comment="s3文件路径",
    )
    file_class = models.CharField(
        max_length=32,
        choices=const.FileClass.choices,
        default=const.FileClass.PRODUCT,
        db_comment="文件类型: 产品文件/完工文件",
    )
    is_enabled = models.IntegerField(
        default=1,
        db_comment="1-可用; 0-不可用",
    )

    create_user = models.CharField(max_length=32, db_comment="创建者")
    created_at = models.DateTimeField(db_comment="创建时间", auto_now_add=True)

    class Meta:
        managed = False
        db_table = "income_order_file"
        ordering = ["-created_at"]

    def __str__(self):
        return f"<IncomeOrderFile: {self.file_name}>"


class IncomeOrderStatusLog(models.Model):
    """订单状态变更日志"""

    order_id = models.IntegerField(db_comment="订单ID")
    total_num = models.CharField(max_length=50, db_comment="订单合成编号")
    # 状态变更类型
    status_type = models.CharField(
        max_length=20,
        choices=const.OrderStatusLogType.choices,
        db_comment="状态类型",
    )
    # 变更前后的状态值
    old_status = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        db_comment="变更前状态",
    )
    new_status = models.CharField(
        max_length=50,
        db_comment="变更后状态",
    )
    remark = models.TextField(
        blank=True,
        null=True,
        db_comment="备注信息",
    )
    # 是否回退操作
    is_rollback = models.BooleanField(
        default=False,
        db_comment="是否为回退操作",
    )
    # 操作信息
    operator = models.CharField(max_length=50, db_comment="操作人")

    created_at = models.DateTimeField(db_comment="创建时间", auto_now_add=True)

    class Meta:
        managed = False
        db_table = "income_order_status_log"
        ordering = ["created_at"]
        indexes = [
            models.Index(fields=["order_id"]),
            models.Index(fields=["total_num"]),
            models.Index(fields=["status_type"]),
            models.Index(fields=["created_at"]),
        ]

    def __str__(self):
        return f"<IncomeOrderStatusLog: {self.total_num} - {self.status_type}>"
