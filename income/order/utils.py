from income import const
from income.contract.models import ContractInfo
from income.customer.models import CustomerInfo

from .models import IncomeOrderStatusLog


def is_rollback_operation(status_type, old_status, new_status):
    """
    判断是否为回退操作

    Args:
        status_type: 状态类型 ('service_status' 或 'bill_status')
        old_status: 变更前状态
        new_status: 变更后状态

    Returns:
        bool: True表示回退操作, False表示正常操作
    """
    if status_type == const.OrderStatusLogType.SERVICE_STATUS:
        # 服务状态的正常流程顺序
        service_flow = const.OrderServiceStatus.values

        try:
            old_index = service_flow.index(old_status)
            new_index = service_flow.index(new_status)
            # 如果新状态的索引小于旧状态的索引,则为回退操作
        except ValueError:
            # 如果状态不在预定义流程中,无法判断,默认为非回退
            return False
        else:
            return new_index < old_index

    elif status_type == const.OrderStatusLogType.BILL_STATUS:
        # 计费状态的正常流程顺序
        bill_flow = const.OrderBillStatus.values

        try:
            old_index = bill_flow.index(old_status)
            new_index = bill_flow.index(new_status)
            # 如果新状态的索引小于旧状态的索引,则为回退操作
        except ValueError:
            # 如果状态不在预定义流程中,无法判断,默认为非回退
            return False
        else:
            return new_index < old_index

    return False


def log_order_status_change(
    order_instance,
    status_type,
    old_status,
    new_status,
    operator,
    remark=None,
):
    """
    记录订单状态变更日志

    Args:
        order_instance: 订单实例
        status_type: 状态类型 ('service_status' 或 'bill_status')
        old_status: 变更前状态
        new_status: 变更后状态
        operator: 操作人
        remark: 备注信息
    """
    if old_status != new_status:
        # 检测是否为回退操作
        is_rollback = is_rollback_operation(status_type, old_status, new_status)

        IncomeOrderStatusLog.objects.create(
            order_id=order_instance.id,
            total_num=order_instance.total_num,
            status_type=status_type,
            old_status=old_status,
            new_status=new_status,
            remark=remark,
            is_rollback=is_rollback,
            operator=operator,
        )


def order_info_add_extra_field(query_data_list: list):
    """添加额外字段

    :param query_data_list: 订单信息的查询结果
    :return:
    """
    contract_num_list = [query_data["contract_num"] for query_data in query_data_list]
    customer_num_list = [query_data["customer_num"] for query_data in query_data_list]

    contract_qs = ContractInfo.objects.filter(
        contract_num__in=set(contract_num_list),
    ).values_list("contract_num", "contract_title")
    contract_map = dict(contract_qs)

    customer_qs = CustomerInfo.objects.filter(
        customer_num__in=set(customer_num_list),
    ).values_list("customer_num", "customer_name")
    customer_map = dict(customer_qs)
    for query_data in query_data_list:
        query_data["contract_title"] = contract_map.get(query_data["contract_num"])
        query_data["customer_name"] = customer_map.get(query_data["customer_num"])
        query_data["created_at"] = query_data["created_at"].strftime("%s")
    return query_data_list


def order_info_add_extra_field_for_retrieve(query_data: dict):
    """添加额外字段

    :param query_data: 订单信息的查询结果
    :return:
    """
    contract_num = query_data["contract_num"]
    customer_num = query_data["customer_num"]
    contract_info = ContractInfo.objects.filter(
        contract_num=contract_num,
    ).values(
        "contract_title",
        "contract_legal_num",
        "sign_contract_entity",
        "business",
    ).first()
    customer_info = CustomerInfo.objects.filter(
        customer_num=customer_num,
    ).values_list("customer_num", "customer_name").first()
    query_data.update(contract_info)
    query_data["customer_name"] = customer_info[1]
    return query_data
