from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import extend_schema_view
from rest_framework import mixins
from rest_framework.decorators import action
from rest_framework.exceptions import ParseError
from rest_framework.response import Response

from income import const
from income import message
from income.contrib.drf.filters import SearchAndFilter
from income.contrib.drf.views import CreateModelMixin
from income.contrib.drf.views import GenericViewSet
from income.contrib.drf.views import UpdateModelMixin
from income.operation_log.utils import OperationLogManager
from income.permissions import IsAuthenticated
from income.permissions import RoleMenuPermission
from income.utils.file_handler import file_preview

from .filters import ContractLegalNumberFilter
from .filters import CustomerNumFilter
from .filters import OrderStatusLogStatusTypeFilter
from .models import IncomeOrderFile
from .models import IncomeOrderInfo
from .models import IncomeOrderStatusLog
from .permissions import OrderInfoPermission
from .serializers import ChangeOrderSerializer
from .serializers import NewSubOrderSerializer
from .serializers import OrderChangeBillStatusSerializer
from .serializers import OrderChangeServiceStatusSerializer
from .serializers import OrderFileSerializer
from .serializers import OrderInfoSerializer
from .serializers import OrderStatusLogSerializer
from .serializers import RenewalOrderSerializer
from .utils import order_info_add_extra_field
from .utils import order_info_add_extra_field_for_retrieve


@extend_schema_view(
    list=extend_schema(summary="获取收入订单信息"),
    create=extend_schema(summary="创建收入订单信息"),
    update=extend_schema(summary="更新ID对应的收入订单信息"),
    retrieve=extend_schema(summary="根据ID获取收入订单的详细信息"),
    change_service_status=extend_schema(summary="更新服务状态"),
    change_bill_status=extend_schema(summary="更新计费状态"),
    change_order=extend_schema(summary="变更订单信息"),
    new_sub_order=extend_schema(summary="新增子订单"),
    renewal_order=extend_schema(summary="续约订单"),
)
@extend_schema(tags=["order-info"])
class OrderInfoViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
    CreateModelMixin,
    UpdateModelMixin,
):
    """订单信息"""

    serializer_class = OrderInfoSerializer
    serializers = {
        "change_service_status": OrderChangeServiceStatusSerializer,
        "change_bill_status": OrderChangeBillStatusSerializer,
        "change_order": ChangeOrderSerializer,
        "new_sub_order": NewSubOrderSerializer,
        "renewal_order": RenewalOrderSerializer,
    }
    permission_classes = (IsAuthenticated, RoleMenuPermission)
    filter_backends = (
        SearchAndFilter,
        ContractLegalNumberFilter,
        CustomerNumFilter,
    )
    search_fields = [
        "total_num",
        "create_user",
        "business_product_type",
        "order_type",
        "service_type",
        "pay_type",
        "service_status",
        "bill_status",
    ]
    search_contains = True

    identify = const.MenuIdentify.INCOME_ORDER

    def get_queryset(self):
        # 查询id小200的订单信息
        return IncomeOrderInfo.objects.all()

    def get_serializer_class(self):
        return self.serializers.get(self.action, self.serializer_class)

    def update(self, request, *args, **kwargs):
        instance = self.get_object(for_update=True)
        if instance.service_status != const.OrderServiceStatus.DRAFT:
            raise ParseError(message.ORDER_SERVICE_STATUS_NOT_MODIFY)
        serializer = self.get_serializer(instance, data=request.data, partial=False)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        return Response(serializer.data)

    def list(self, request, *args, **kwargs):
        fields = [field.name for field in IncomeOrderInfo._meta.fields]  # noqa: SLF001
        fields.remove("updated_at")
        queryset = self.filter_queryset(
            self.get_queryset(),
        ).values(*fields)
        page = self.paginate_queryset(queryset)
        if page is not None:
            data = order_info_add_extra_field(page)
            return self.get_paginated_response(data)
        data = order_info_add_extra_field(queryset)
        return Response(data)

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        data = order_info_add_extra_field_for_retrieve(serializer.data)
        return Response(data)

    @extend_schema(
        tags=["order-info"],
        request=OrderChangeServiceStatusSerializer,
        summary="更新服务状态",
        description="更新服务状态",
    )
    @action(
        detail=True,
        methods=["post"],
        url_path="change-service-status",
    )
    def change_service_status(self, request, *args, **kwargs):
        """更新服务状态"""
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    @extend_schema(
        tags=["order-info"],
        request=OrderChangeBillStatusSerializer,
        summary="更新计费状态",
        description="更新计费状态",
    )
    @action(
        detail=True,
        methods=["post"],
        url_path="change-billing-status",
    )
    def change_bill_status(self, request, *args, **kwargs):
        """更新计费状态"""
        instance = self.get_object()
        # 当订单的服务状态在以下范围时不可进行计费审核
        if instance.service_status in [
            const.OrderServiceStatus.DRAFT,
            const.OrderServiceStatus.ORDER_REVIEW,
            const.OrderServiceStatus.DISPATCHED,
            const.OrderServiceStatus.DECOM_ROLLBACK,
            const.OrderServiceStatus.DECOM_ORDER_REVIEW,
            const.OrderServiceStatus.DECOM_DISPATCHED,
            const.OrderServiceStatus.CHG_DECOM_ROLLBACK,
            const.OrderServiceStatus.CHG_DECOM_ORDER_REVIEW,
            const.OrderServiceStatus.CHG_DECOM_DISPATCHED,
        ]:
            raise ParseError(message.ORDER_SERVICE_STATUS_NOT_CHANGE_BILL_STATUS)
        serializer = self.get_serializer(instance, data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    @extend_schema(
        tags=["order-info"],
        request=ChangeOrderSerializer,
        summary="变更订单信息",
        description="变更订单信息, 将charge_order_num向上加1, BG0->BG1",
    )
    @action(
        detail=True,
        methods=["post"],
        url_path="change-order",
    )
    def change_order(self, request, *args, **kwargs):
        """变更订单信息"""
        parent_order = self.get_object()
        serializer = self.get_serializer(
            data=request.data,
            context={"request": request, "parent_order": parent_order},
        )
        serializer.is_valid(raise_exception=True)
        instance = serializer.save()
        # 记录操作日志
        OperationLogManager().log_operation(
            request=request,
            instance_or_queryset=instance,
            operation_type=const.OperationType.CREATE,
            operation_content=f"变更订单-{instance.total_num}",
        )
        return Response(serializer.data)

    @extend_schema(
        tags=["order-info"],
        request=NewSubOrderSerializer,
        summary="新增子订单",
        description="新增子订单, 将sub_order_num的值向上变动一位, charge_order_num设置为默认值BG0",  # noqa: E501
    )
    @action(
        detail=True,
        methods=["post"],
        url_path="new-sub-order",
    )
    def new_sub_order(self, request, *args, **kwargs):
        """新增子订单"""
        parent_order = self.get_object()
        serializer = self.get_serializer(
            data=request.data,
            context={"request": request, "parent_order": parent_order},
        )
        serializer.is_valid(raise_exception=True)
        instance = serializer.save()
        # 记录操作日志
        OperationLogManager().log_operation(
            request=request,
            instance_or_queryset=instance,
            operation_type=const.OperationType.CREATE,
            operation_content=f"新增子订单-{instance.total_num}",
        )
        return Response(serializer.data)

    @extend_schema(
        tags=["order-info"],
        request=RenewalOrderSerializer,
        summary="续约订单",
        description="续约订单,重新生成order_num和total_num,设置服务类型为续约",
    )
    @action(
        detail=True,
        methods=["post"],
        url_path="renewal-order",
    )
    def renewal_order(self, request, *args, **kwargs):
        """续约订单"""
        parent_order = self.get_object()
        serializer = self.get_serializer(
            data=request.data,
            context={"request": request, "parent_order": parent_order},
        )
        serializer.is_valid(raise_exception=True)
        instance = serializer.save()
        # 记录操作日志
        OperationLogManager().log_operation(
            request=request,
            instance_or_queryset=instance,
            operation_type=const.OperationType.CREATE,
            operation_content=f"续约订单-{instance.total_num}",
        )
        return Response(serializer.data)


@extend_schema_view(
    list=extend_schema(summary="获取订单文件信息"),
    attachment_preview=extend_schema(
        summary="预览附件",
        responses={200: {"description": "FileStream"}},
    ),
)
@extend_schema(tags=["order-file"])
class OrderFileViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
):
    """订单文件"""

    NOT_FOUND_MESSAGE = message.ORDER_FILE_NOT_FOUND

    serializer_class = OrderFileSerializer
    permission_classes = (IsAuthenticated, RoleMenuPermission, OrderInfoPermission)
    pagination_class = None

    identify = const.MenuIdentify.INCOME_ORDER

    def get_queryset(self):
        return IncomeOrderFile.objects.filter(order_id=self.kwargs["order_id"])

    @action(
        methods=["get"],
        detail=True,
        url_path="attachment-preview",
    )
    def attachment_preview(self, request, *args, **kwargs):
        """预览附件"""
        # 获取订单文件对象
        order_file = self.get_object()
        file_info = IncomeOrderFile.objects.values("file_path", "file_name").get(
            id=order_file.id,
        )
        return file_preview(file_info["file_name"], file_info["file_path"])


@extend_schema_view(
    list=extend_schema(summary="获取订单状态变更日志"),
)
@extend_schema(tags=["order-status-log"])
class OrderStatusLogViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
):
    """订单状态变更日志"""

    serializer_class = OrderStatusLogSerializer
    permission_classes = (IsAuthenticated, RoleMenuPermission, OrderInfoPermission)
    pagination_class = None
    filter_backends = [OrderStatusLogStatusTypeFilter]
    status_type_choices = const.OrderStatusLogType

    identify = const.MenuIdentify.INCOME_ORDER

    def get_queryset(self):
        """获取指定订单的状态变更日志"""
        return IncomeOrderStatusLog.objects.filter(
            order_id=self.kwargs["order_id"],
        )
