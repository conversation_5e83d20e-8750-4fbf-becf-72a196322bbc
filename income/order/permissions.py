from rest_framework.permissions import BasePermission

from income import message
from income.contrib.drf.shortcuts import get_object_or_404

from .models import IncomeOrderInfo


class OrderInfoPermission(BasePermission):
    """订单信息权限"""

    def has_permission(self, request, view):
        get_object_or_404(
            IncomeOrderInfo.objects.all(),
            pk=view.kwargs["order_id"],
            error_message=message.ORDER_NOT_FOUND,
        )
        return True
