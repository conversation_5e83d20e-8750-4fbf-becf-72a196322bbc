from django.urls import include
from django.urls import path
from rest_framework.routers import SimpleRouter

from .views import OrderFileViewSet
from .views import OrderInfoViewSet
from .views import OrderStatusLogViewSet

# 订单信息
router = SimpleRouter(trailing_slash=False)
router.register("orders-info", OrderInfoViewSet, basename="order_info")

# 订单文件
file_router = SimpleRouter(trailing_slash=False)
file_router.register("order-files", OrderFileViewSet, basename="order_file")

# 订单状态变更日志
status_log_router = SimpleRouter(trailing_slash=False)
status_log_router.register(
    "order-status-log",
    OrderStatusLogViewSet,
    basename="order_status_log",
)

urlpatterns = [
    path("", include(router.urls)),
    path("orders/<int:order_id>/", include(file_router.urls)),
    path("orders/<int:order_id>/", include(status_log_router.urls)),
]
