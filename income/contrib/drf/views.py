from rest_framework import status
from rest_framework.response import Response
from rest_framework.settings import api_settings
from rest_framework.viewsets import GenericViewSet as BaseGenericViewSet

from income import const
from income.operation_log.utils import OperationLogManager

from .shortcuts import get_object_or_404


class GenericViewSet(BaseGenericViewSet):
    lookup_value_regex = "[0-9]+"

    def get_object(self, for_update=False):
        queryset = self.filter_queryset(self.get_queryset())
        if for_update:
            queryset = queryset.select_for_update()
        lookup_url_kwarg = self.lookup_url_kwarg or self.lookup_field
        assert lookup_url_kwarg in self.kwargs, (
            "Expected view %s to be called with a URL keyword argument "
            'named "%s". Fix your URL conf, or set the `.lookup_field` '
            "attribute on the view correctly."
            % (self.__class__.__name__, lookup_url_kwarg)
        )
        filter_kwargs = {self.lookup_field: self.kwargs[lookup_url_kwarg]}
        error_message = getattr(self, "NOT_FOUND_MESSAGE", "NOT FOUND")
        obj = get_object_or_404(
            queryset,
            error_message=error_message,
            **filter_kwargs,
        )
        self.check_object_permissions(self.request, obj)
        return obj


class UpdateModelMixin:
    """
    Update a model instance.
    """

    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=False)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        if getattr(instance, "_prefetched_objects_cache", None):
            # If 'prefetch_related' has been applied to a queryset, we need to
            # forcibly invalidate the prefetch cache on the instance.
            instance._prefetched_objects_cache = {}

        return Response(serializer.data)

    def perform_update(self, serializer):
        # 获取更新前的实例
        old_instance = self.get_object()
        # 创建一个副本用于比较
        from copy import deepcopy

        old_instance_copy = deepcopy(old_instance)

        instance = serializer.save()
        OperationLogManager().log_operation(
            request=self.request,
            instance_or_queryset=instance,
            operation_type=const.OperationType.UPDATE,
            old_instance=old_instance_copy,
        )


class CreateModelMixin:
    """
    Create a model instance.
    """

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(
            serializer.data,
            status=status.HTTP_201_CREATED,
            headers=headers,
        )

    def perform_create(self, serializer):
        serializer.save()

        instance = serializer.instance
        OperationLogManager().log_operation(
            request=self.request,
            instance_or_queryset=instance,
            operation_type=const.OperationType.CREATE,
        )

    def get_success_headers(self, data):
        try:
            return {"Location": str(data[api_settings.URL_FIELD_NAME])}
        except (TypeError, KeyError):
            return {}
