from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import extend_schema_view
from rest_framework import mixins
from rest_framework.decorators import action
from rest_framework.response import Response

from income import const
from income.contrib.drf.filters import SearchAndFilter
from income.contrib.drf.views import GenericViewSet
from income.customer.filters import CustomerNumberFilter
from income.invoice.serializers import PrePaidInvoiceIssuanceSerializer
from income.invoice.serializers import PrePaidInvoiceSerializer
from income.invoice.utils import add_extra_field
from income.order.models import IncomeOrderInfo
from income.permissions import IsAuthenticated
from income.permissions import RoleMenuPermission


@extend_schema_view(
    list=extend_schema(summary="获取待开票的订单信息(预付费开票)"),
    issuance=extend_schema(summary="开票"),
)
@extend_schema(tags=["pre-paid-invoice"])
class PrePaidInvoiceViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
):
    """待开票的订单信息(预付费开票)"""

    serializer_class = PrePaidInvoiceSerializer
    serializers = {
        "issuance": PrePaidInvoiceIssuanceSerializer,
    }
    permission_classes = [IsAuthenticated, RoleMenuPermission]
    filter_backends = [SearchAndFilter, CustomerNumberFilter]
    search_fields = ["account_seq", "total_num"]
    search_contains = True

    def get_queryset(self):
        return IncomeOrderInfo.objects.filter(
            bill_status__in=[
                const.OrderBillStatus.IN_PROGRESS,
                const.OrderBillStatus.DECOM_BILLING_CONFIRMED,
                const.OrderBillStatus.CHG_DECOM_BILLING_CONFIRMED,
            ],
            pay_type=const.PayType.PREPAID,
        )

    def get_serializer_class(self, *args, **kwargs):
        return self.serializers.get(self.action, self.serializer_class)

    def list(self, request, *args, **kwargs):
        fields = (
            "id",
            "order_num",
            "total_num",
            "account_seq",
            "bill_status",
            "customer_num",
            "product_main_category",
            "pay_cycle",
            "currency_type",
        )
        queryset = self.filter_queryset(
            self.get_queryset(),
        ).values(*fields)
        page = self.paginate_queryset(queryset)
        if page is not None:
            data = add_extra_field(page)
            return self.get_paginated_response(data)
        data = add_extra_field(queryset)
        return Response(data)

    @action(
        methods=["post"],
        detail=False,
        url_path="issuance",
    )
    def issuance(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)
