from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import extend_schema_view
from rest_framework import mixins
from rest_framework.decorators import action
from rest_framework.response import Response

from income import const
from income.charge.filters import IncomeChargeDetailFieldsFilter
from income.charge.models import IncomeChargeDetail
from income.contrib.drf.views import GenericViewSet
from income.invoice.serializers import NoInvoicingSerializer
from income.invoice.serializers import OnIssuanceSerializer
from income.permissions import IsAuthenticated
from income.permissions import RoleMenuPermission


@extend_schema_view(
    list=extend_schema(summary="获取不开票的权责信息"),
    on_issuance=extend_schema(summary="打开开票"),
)
@extend_schema(tags=["no-invoicing"])
class NoInvoicingViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
):
    """不开票的权责信息"""

    serializer_class = NoInvoicingSerializer
    serializers = {
        "on_issuance": OnIssuanceSerializer,
    }
    permission_classes = [IsAuthenticated, RoleMenuPermission]
    filter_backends = [IncomeChargeDetailFieldsFilter]
    search_fields = ["sub_order_no", "account_seq", "charge_month", "customer_name"]
    search_contains = True
    table_mapping = {
        "sub_order_no": "a",
        "account_seq": "a",
        "charge_month": "a",
        "customer_name": "b",
    }

    identify = const.MenuIdentify.INVOICE

    def get_queryset(self):
        """
        使用Raw SQL获取完整数据, 减少内存使用
        """
        return self._get_raw_queryset()

    def get_serializer_class(self):
        return self.serializers.get(self.action, self.serializer_class)

    def _get_raw_queryset(self):
        """使用Raw SQL获取完整数据"""
        sql = """
        SELECT
            a.`id`,
            a.`sub_order_no`,
            a.`account_seq`,
            a.`income_type`,
            a.`pay_type`,
            a.`tax`,
            a.`charge_month`,
            a.`fee_amount`,
            b.`customer_name`,
            c.`adjust_month`
        FROM
            `income_charge_detail` a
            LEFT JOIN `customer_info` b ON a. `customer_num` = b. `customer_num`
            LEFT JOIN `income_adjust_detail` c ON a. `income_adjust_id` = c.id
        WHERE
            a.`need_invoice` = 0
        ORDER BY a.`created_at` DESC
        """
        return IncomeChargeDetail.objects.raw(sql)

    @action(
        methods=["post"],
        detail=False,
        url_path="on-issuance",
    )
    def on_issuance(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)
