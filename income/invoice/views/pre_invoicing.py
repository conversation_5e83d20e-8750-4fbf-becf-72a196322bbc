from pathlib import Path

from django.conf import settings
from django.http import FileResponse
from django.utils import timezone
from django.utils.encoding import escape_uri_path
from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import extend_schema_view
from rest_framework import mixins
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.exceptions import ParseError
from rest_framework.parsers import MultiPartParser
from rest_framework.response import Response

from income import const
from income import message
from income.charge.filters import IncomeChargeDetailFieldsFilter
from income.contrib.drf.filters import SearchAndFilter
from income.contrib.drf.shortcuts import get_object_or_404
from income.contrib.drf.views import GenericViewSet
from income.contrib.drf.views import UpdateModelMixin
from income.customer.filters import CustomerNumberFilter
from income.invoice.models import IncomeInvoice
from income.invoice.models import IncomeInvoiceDetail
from income.invoice.models import IncomeInvoiceFile
from income.invoice.serializers import PreInvoicingDetailSerializer
from income.invoice.serializers import PreInvoicingIssuanceSerializer
from income.invoice.serializers import PreInvoicingSerializer
from income.invoice.serializers import PreInvoicingUpdateSerializer
from income.invoice.serializers import PreInvoicingUploadAttachmentSerializer
from income.invoice.utils import export_to_excel
from income.invoice.utils import invoice_add_extra_field
from income.permissions import IsAuthenticated
from income.permissions import RoleMenuPermission
from income.utils.file_handler import file_preview


@extend_schema_view(
    list=extend_schema(summary="获取预开票的列表信息"),
    issuance=extend_schema(summary="开票"),
    update=extend_schema(summary="更新预开票信息(state=pending时)"),
    export=extend_schema(summary="导出筛选后的预开票信息"),
    attachment_upload=extend_schema(
        summary="发票类型为'invoice'的预开票信息上传附件",
        description="限制类型为'invoice'、附件格式限制为pdf",
    ),
    attachment_preview=extend_schema(
        summary="预览附件",
        responses={200: {"description": "FileStream"}},
    ),
    attachment_download=extend_schema(
        summary="下载附件",
        responses={200: {"description": "下载链接"}},
    ),
    attachment_delete=extend_schema(
        summary="删除附件",
        responses={204: None},
    ),
)
@extend_schema(tags=["pre-invoicing"])
class PreInvoicingViewSet(
    GenericViewSet,
    UpdateModelMixin,
):
    """预开票"""

    NOT_FOUND_MESSAGE = message.INVOICE_NOT_FOUND

    serializer_class = PreInvoicingSerializer
    serializers = {
        "issuance": PreInvoicingIssuanceSerializer,
        "update": PreInvoicingUpdateSerializer,
        "attachment_upload": PreInvoicingUploadAttachmentSerializer,
    }
    permission_classes = [IsAuthenticated, RoleMenuPermission]
    filter_backends = [SearchAndFilter, CustomerNumberFilter]
    search_fields = ["account_seq", "create_user", "customer_num"]
    search_contains = True
    table_mapping = {
        "account_seq": "a",
        "create_user": "a",
        "customer_num": "a",
    }

    identify = const.MenuIdentify.INVOICE

    def get_queryset(self):
        return IncomeInvoice.objects.filter(
            state=const.InvoiceState.PENDING,
        )

    def get_serializer_class(self):
        return self.serializers.get(self.action, self.serializer_class)

    def list(self, request, *args, **kwargs):
        fields = [
            "id",
            "invoice_no",
            "amount",
            "current_amount",
            "account_seq",
            "invoice_info_id",
            "invoice_type",
            "tax_rate",
            "tax_amount",
            "currency_type",
            "invoice_currency_type",
            "state",
            "exchange_rate",
            "signing_entity",
            "customer_num",
            "invoice_file_id",
            "remark",
            "invoice_pay_type",
            "create_user",
            "created_at",
        ]
        queryset = self.filter_queryset(
            self.get_queryset(),
        ).values(*fields)
        page = self.paginate_queryset(queryset)
        if page is not None:
            data = invoice_add_extra_field(page)
            return self.get_paginated_response(data)
        data = invoice_add_extra_field(queryset)
        return Response(data)

    @action(
        methods=["post"],
        detail=False,
        url_path="issuance",
    )
    def issuance(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    def _get_raw_queryset(self):
        """使用Raw SQL获取完整数据"""
        sql = """
        SELECT
            a.`id`,
            a.`invoice_no`,
            a.`amount`,
            a.`account_seq`,
            a.`invoice_type`,
            a.`tax_rate`,
            a.`tax_amount`,
            a.`currency_type`,
            a.`invoice_currency_type`,
            a.`state`,
            a.`exchange_rate`,
            a.`signing_entity`,
            a.`remark`,
            b.`customer_name`,
            c.`customer_invoice_name`
        FROM
            `income_invoice` a
            LEFT JOIN `customer_info` b ON a.`customer_num` = b.`customer_num`
            LEFT JOIN `income_invoice_info` c ON a.`invoice_info_id` = c.`id`
        WHERE
            a.`state` = 'pending'
        ORDER BY a.`created_at` DESC
        """
        return IncomeInvoice.objects.raw(sql)

    @action(
        methods=["get"],
        detail=False,
        url_path="export",
        filter_backends=[IncomeChargeDetailFieldsFilter],
        pagination_class=None,
    )
    def export(self, request, *args, **kwargs):
        """导出预开票数据为Excel文件"""
        # 获取筛选后的数据
        queryset = self.filter_queryset(self._get_raw_queryset())

        # 转换为DataFrame并导出Excel
        return export_to_excel(queryset)

    @action(
        methods=["post"],
        detail=True,
        url_path="attachment-upload",
        parser_classes=[MultiPartParser],
    )
    def attachment_upload(self, request, *args, **kwargs):
        """上传附件"""
        invoice = self.get_object()
        # 如果发票类型不是invoice或者发票已存在文件时, 不允许进行上传
        if (
            invoice.invoice_type != const.InvoiceType.INVOICE
            or invoice.invoice_file_id is not None
        ):
            raise ParseError(message.INV_TYPE_NOT_SUPPORT_ATTACHMENT)
        context = {"request": request, "invoice": invoice}
        serializer = self.get_serializer(data=request.data, context=context)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    @action(
        methods=["get"],
        detail=True,
        url_path="attachment-preview",
    )
    def attachment_preview(self, request, *args, **kwargs):
        """预览附件"""
        # 获取发票对象并进行权限检查
        invoice = self.get_object()
        file_info = IncomeInvoiceFile.objects.values("file_path", "file_name").get(
            id=invoice.invoice_file_id,
        )
        return file_preview(file_info["file_name"], file_info["file_path"])

    @action(
        methods=["get"],
        detail=True,
        url_path="attachment-download",
    )
    def attachment_download(self, request, *args, **kwargs):
        """下载附件"""
        # 获取发票对象并进行权限检查
        invoice = self.get_object()
        file_info = IncomeInvoiceFile.objects.values("file_path", "file_name").get(
            id=invoice.invoice_file_id,
        )
        base_path = Path(settings.MEDIA_ROOT)
        file_full_path = base_path / file_info["file_path"]

        # 检查文件是否存在
        if not file_full_path.exists():
            raise ParseError(message.ATTACHMENT_FILE_NOT_FOUND)

        response = FileResponse(
            Path.open(file_full_path, "rb"),
            as_attachment=True,
            filename=file_info["file_name"],
        )
        response["Content-Disposition"] = (
            f'attachment; filename="{escape_uri_path(file_info["file_name"])}"'
        )
        return response

    @action(
        methods=["delete"],
        detail=True,
        url_path="attachment-delete",
    )
    def attachment_delete(self, request, *args, **kwargs):
        """删除附件"""
        invoice = self.get_object()
        file_instance = IncomeInvoiceFile.objects.get(id=invoice.invoice_file_id)
        # 删除物理文件
        from income.utils.file_handler import FileHandler

        file_handler = FileHandler()
        file_handler.delete_file(file_instance.file_path)
        # 删除数据库记录
        file_instance.delete()
        # 更新发票记录, 移除文件关联
        invoice.invoice_file_id = None
        invoice.save(update_fields=["invoice_file_id", "updated_at"])
        return Response({"message": "附件删除成功"})


@extend_schema_view(
    list=extend_schema(summary="获取预开票的详情"),
    delete=extend_schema(summary="删除预开票的详情"),
)
@extend_schema(tags=["pre-invoicing-detail"])
class PreInvoicingDetailViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
    mixins.DestroyModelMixin,
):
    """预开票详情"""

    NOT_FOUND_MESSAGE = message.INVOICE_DETAIL_NOT_FOUND

    serializer_class = PreInvoicingDetailSerializer
    permission_classes = [IsAuthenticated, RoleMenuPermission]
    filter_backends = [IncomeChargeDetailFieldsFilter]
    search_fields = ["sub_order_no", "total_num"]
    search_contains = True
    table_mapping = {
        "sub_order_no": "c",
        "total_num": "o",
    }
    query_params = []

    identify = const.MenuIdentify.INVOICE

    def get_queryset(self):
        """使用Raw SQL根据target_type获取不同的详情数据"""
        sql = f"""
        SELECT
            a.id,
            a.invoice_id,
            a.target_id,
            a.target_type,
            a.amount,
            a.invoice_month,
            a.state,
            a.created_at,
            CASE
                WHEN a.target_type = 'bill' THEN c.sub_order_no
                ELSE NULL
            END as sub_order_no,
            CASE
                WHEN a.target_type = 'bill' THEN c.charge_month
                ELSE NULL
            END as charge_month,
            CASE
                WHEN a.target_type = 'bill' AND c.income_adjust_id IS NOT NULL
                THEN adjust.adjust_month
                ELSE NULL
            END as adjust_month,
            CASE
                WHEN a.target_type = 'instance' THEN o.total_num
                ELSE NULL
            END as total_num
        FROM
            income_invoice_detail a
            LEFT JOIN income_charge_detail c
                ON a.target_type = 'bill' AND a.target_id = c.id
            LEFT JOIN income_adjust_detail adjust ON c.income_adjust_id = adjust.id
            LEFT JOIN income_order_info o
                ON a.target_type = 'instance' AND a.target_id = o.id
        WHERE
            a.invoice_id = {self.kwargs["invoice_id"]}
        ORDER BY a.`created_at` DESC
        """
        return IncomeInvoiceDetail.objects.raw(sql)

    def get_queryset_for_destroy(self):
        return IncomeInvoiceDetail.objects.filter(invoice_id=self.kwargs["invoice_id"])

    def destroy(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset_for_destroy())
        filter_kwargs = {self.lookup_field: self.kwargs["pk"]}
        instance = get_object_or_404(
            queryset,
            error_message=self.NOT_FOUND_MESSAGE,
            **filter_kwargs,
        )
        self.perform_destroy(instance)
        return Response(
            status=status.HTTP_200_OK,
            data={"message": "删除预开票详情成功"},
        )

    def perform_destroy(self, instance):
        # 删除当前的invoice_detail的值后,
        # 需要对关联的上层invoice的值的amount、current_amount、tax_mount进行重新计算
        # amount = amount - instance.amount  # noqa: ERA001
        # current_amount = amount * exchange_rate  # noqa: ERA001

        # tax_amount = amount - (amount / ( 1 + tax/100)
        from django.db.models import F

        IncomeInvoice.objects.filter(pk=instance.invoice_id).update(
            amount=F("amount") - instance.amount,
            current_amount=F("amount") * F("exchange_rate"),
            tax_amount=(
                F("current_amount") - (F("current_amount") / (1 + F("tax_rate") / 100))
            ),
            updated_at=timezone.now(),
        )
        instance.delete()
