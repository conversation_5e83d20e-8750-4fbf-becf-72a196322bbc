from django.db import transaction
from django.db.models import F
from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import extend_schema_view
from rest_framework import mixins
from rest_framework.decorators import action
from rest_framework.exceptions import ParseError
from rest_framework.response import Response

from income import message
from income.charge.filters import IncomeChargeDetailFieldsFilter
from income.const import InvoiceApprovedState
from income.const import InvoiceState
from income.const import MenuIdentify
from income.contrib.drf.filters import SearchAndFilter
from income.contrib.drf.views import GenericViewSet
from income.customer.filters import CustomerNumberFilter
from income.invoice.models import IncomeInvoice
from income.invoice.serializers import PreSubmitInvoiceSerializer
from income.invoice.serializers import RecognitionInvoiceSerializer
from income.invoice.serializers import SubmitApprovalSerializer
from income.invoice.utils import group_approved_state_export_excel
from income.invoice.utils import invoice_add_extra_field
from income.permissions import IsAuthenticated
from income.permissions import RoleMenuPermission


@extend_schema_view(
    list=extend_schema(summary="获取预提交发票的列表信息"),
    submit_approval=extend_schema(summary="提交审批"),
    revert_to_pending=extend_schema(summary="返回预开票状态"),
    export=extend_schema(summary="导出发票数据"),
)
@extend_schema(tags=["pre-submit"])
class PreSubmitInvoiceViewSet(GenericViewSet):
    """预提交发票视图集"""

    serializer_class = PreSubmitInvoiceSerializer
    serializers = {
        "submit_approval": SubmitApprovalSerializer,
    }
    permission_classes = [IsAuthenticated, RoleMenuPermission]
    filter_backends = [SearchAndFilter, CustomerNumberFilter]
    search_fields = [
        "account_seq",
        "customer_num",
        "create_user",
    ]
    search_contains = True
    table_mapping = {
        "account_seq": "a",
        "create_user": "a",
        "customer_num": "a",
    }

    identify = MenuIdentify.INVOICE

    def get_queryset(self):
        return IncomeInvoice.objects.filter(
            group_approved_state=InvoiceApprovedState.DRAFT,
        )

    def get_serializer_class(self):
        return self.serializers.get(self.action, self.serializer_class)

    def list(self, request, *args, **kwargs):
        fields = [
            "id",
            "invoice_no",
            "amount",
            "account_seq",
            "invoice_info_id",
            "invoice_type",
            "tax_rate",
            "tax_amount",
            "currency_type",
            "invoice_currency_type",
            "group_approved_state",
            "exchange_rate",
            "signing_entity",
            "customer_num",
            "invoice_file_id",
            "remark",
            "invoice_pay_type",
            "create_user",
            "created_at",
        ]
        queryset = self.filter_queryset(
            self.get_queryset(),
        ).values(*fields)
        page = self.paginate_queryset(queryset)
        if page is not None:
            data = invoice_add_extra_field(page)
            return self.get_paginated_response(data)
        data = invoice_add_extra_field(queryset)
        return Response(data)

    @action(detail=False, methods=["post"], url_path="submit-approval")
    @transaction.atomic
    def submit_approval(self, request, *args, **kwargs):
        """提交审批"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    @action(detail=True, methods=["post"], url_path="revert-to-pending")
    @transaction.atomic
    def revert_to_pending(self, request, *args, **kwargs):
        """返回预开票状态"""
        invoice = self.get_object()
        if invoice.group_approved_state != InvoiceApprovedState.DRAFT:
            raise ParseError(message.INVOICE_NOT_FOUND_OR_INVALID_STATE)

        invoice.state = InvoiceState.PENDING
        invoice.group_approved_state = None
        invoice.save(update_fields=["state", "group_approved_state", "updated_at"])
        return Response({"message": "成功将发票返回预开票状态"})

    def _get_raw_queryset(self):
        """使用Raw SQL获取完整数据"""
        sql = """
        SELECT
            a.`id`,
            a.`invoice_no`,
            a.`amount`,
            a.`account_seq`,
            a.`invoice_type`,
            a.`tax_rate`,
            a.`tax_amount`,
            a.`currency_type`,
            a.`invoice_currency_type`,
            a.`group_approved_state`,
            a.`exchange_rate`,
            a.`signing_entity`,
            a.`remark`,
            b.`customer_name`,
            c.`customer_invoice_name`
        FROM
            `income_invoice` a
            LEFT JOIN `customer_info` b ON a.`customer_num` = b.`customer_num`
            LEFT JOIN `income_invoice_info` c ON a.`invoice_info_id` = c.`id`
        WHERE
            a.`group_approved_state` = 'draft'
        ORDER BY a.`created_at` DESC
        """
        return IncomeInvoice.objects.raw(sql)

    @action(
        detail=False,
        methods=["get"],
        url_path="export",
        filter_backends=[IncomeChargeDetailFieldsFilter],
        pagination_class=None,
    )
    def export(self, request):
        """导出发票数据"""
        return group_approved_state_export_excel(
            self.filter_queryset(self._get_raw_queryset()),
            file_prefix="待提交",
        )

@extend_schema_view(
    list=extend_schema(summary="获取认款发票的列表信息"),
)
@extend_schema(tags=["recognition-invoice"])
class RecognitionInvoiceViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
):
    """认款发票视图集"""

    pagination_class = None
    serializer_class = RecognitionInvoiceSerializer
    permission_classes = [IsAuthenticated, RoleMenuPermission]
    filter_backends = [CustomerNumberFilter]

    identify = MenuIdentify.INVOICE

    def get_queryset(self):
        # 只展示还有可用开票金额的数据
        return IncomeInvoice.objects.filter(
            state=InvoiceState.ISSUED,
            write_off_amount__lt=F("current_amount"),
        )
