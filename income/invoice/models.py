from django.db import models

from income import const


class IncomeInvoice(models.Model):
    """发票"""

    invoice_no = models.CharField(max_length=20, db_comment="发票号")
    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        db_comment="开票金额",
    )
    # amount * 汇率 = 实际开票金额
    current_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        db_comment="实际开票金额",
    )
    account_seq = models.CharField(max_length=20, db_comment="分账序号")
    invoice_info_id = models.IntegerField(db_comment="关联income_invoice_info表id")
    invoice_type = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        db_comment="客户发票类型:增值税专票;增值税普票;invoice;",
    )
    tax_rate = models.IntegerField(blank=True, null=True, db_comment="税率")
    tax_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        db_comment="税额",
    )
    currency_type = models.CharField(
        max_length=10,
        blank=True,
        null=True,
        db_comment="权责/订单币种",
    )
    invoice_currency_type = models.CharField(
        max_length=10,
        blank=True,
        null=True,
        db_comment="需要开具的发票币种",
    )
    state = models.CharField(
        max_length=10,
        choices=const.InvoiceState.choices,
        default=const.InvoiceState.PENDING,
        db_comment="发票状态",
    )
    group_approved_state = models.CharField(
        max_length=10,
        blank=True,
        null=True,
        choices=const.InvoiceApprovedState.choices,
        db_comment="审批状态",
    )
    exchange_rate = models.DecimalField(
        max_digits=18,
        decimal_places=10,
        blank=True,
        null=True,
        db_comment="汇率",
    )
    signing_entity = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        db_comment="开具实体",
    )
    customer_num = models.CharField(max_length=50, blank=True, null=True)
    invoice_file_id = models.IntegerField(blank=True, null=True)
    remark = models.CharField(max_length=1024, blank=True, null=True)
    invoice_pay_type = models.PositiveSmallIntegerField(
        choices=const.InvoicePayType.choices,
        default=const.InvoicePayType.POSTPAID,
        db_comment="付费方式: 预付: 1/后付: 2",
    )
    write_off_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        db_comment="发票核销金额",
    )
    write_off_currency_type = models.CharField(
        max_length=10,
        blank=True,
        null=True,
        db_comment="核销币种",
    )

    create_user = models.CharField(max_length=40, blank=True, null=True)

    created_at = models.DateTimeField(db_comment="创建时间", auto_now_add=True)
    updated_at = models.DateTimeField(db_comment="更新时间", auto_now=True)

    class Meta:
        managed = False
        db_table = "income_invoice"
        ordering = ("-created_at",)
        db_table_comment = "发票表"

    def __str__(self):
        return f"<IncomeInvoice: {self.invoice_no}>"


class IncomeInvoiceDetail(models.Model):
    """发票明细"""

    invoice_id = models.IntegerField(db_comment="发票id")
    target_id = models.IntegerField(blank=True, null=True, db_comment="权责id或订单id")
    target_type = models.CharField(
        max_length=10,
        choices=const.InvoiceTargetType.choices,
        db_comment="bill:权责,instance:订单",
    )
    amount = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)
    invoice_month = models.IntegerField(
        blank=True,
        null=True,
        db_comment="开具发票的账期",
    )
    state = models.IntegerField(
        choices=const.InvoiceDetailState.choices,
        default=const.InvoiceDetailState.VALID,
        db_comment="发票状态",
    )

    created_at = models.DateTimeField(db_comment="创建时间", auto_now_add=True)
    updated_at = models.DateTimeField(db_comment="更新时间", auto_now=True)

    class Meta:
        managed = False
        db_table = "income_invoice_detail"
        ordering = ("-created_at",)
        db_table_comment = "发票明细表"

    def __str__(self):
        return f"<IncomeInvoiceDetail: {self.id}>"


class IncomeInvoiceFile(models.Model):
    """发票文件"""

    file_name = models.CharField(max_length=100, blank=True, null=True)
    file_path = models.CharField(max_length=200, blank=True, null=True)
    save_file_name = models.CharField(max_length=100, blank=True, null=True)
    create_user = models.CharField(max_length=40, blank=True, null=True)

    created_at = models.DateTimeField(db_comment="创建时间", auto_now_add=True)
    updated_at = models.DateTimeField(db_comment="更新时间", auto_now=True)

    class Meta:
        managed = False
        db_table = "income_invoice_file"
        ordering = ("-created_at",)
        db_table_comment = "发票文件表"

    def __str__(self):
        return f"<IncomeInvoiceFile: {self.file_name}>"


class IncomeInvoicingRecord(models.Model):
    """开票记录"""

    task_id = models.CharField(max_length=50, db_comment="开票记录")
    invoice_type = models.SmallIntegerField(
        choices=const.InvoiceRecordType.choices,
        db_comment="开票类型: 1-预开票,2-批量预开票",
    )
    request_info = models.JSONField(
        blank=True,
        null=True,
        db_comment="请求信息",
    )
    account_seq = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        db_comment="分账序号",
    )
    state = models.IntegerField(
        blank=True,
        null=True,
        choices=const.InvoiceRecordState.choices,
        default=const.InvoiceRecordState.PENDING,
        db_comment="状态: 1-开票中,2-开票完成,3-开票失败",
    )
    reason = models.TextField(blank=True, null=True, db_comment="失败原因")

    create_user = models.CharField(max_length=40, blank=True, null=True)
    created_at = models.DateTimeField(db_comment="创建时间", auto_now_add=True)
    updated_at = models.DateTimeField(db_comment="更新时间", auto_now=True)

    class Meta:
        managed = False
        db_table = "income_invoicing_record"
        ordering = ("-created_at",)
        db_table_comment = "开票记录表"

    def __str__(self):
        return f"<IncomeInvoicingRecord: {self.task_id}>"
