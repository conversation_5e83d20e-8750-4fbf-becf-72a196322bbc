from django.urls import include
from django.urls import path
from rest_framework.routers import SimpleRouter

from .views import InvoiceAuditViewSet
from .views import InvoiceRecordViewSet
from .views import NoInvoicingViewSet
from .views import PostPaidInvoiceViewSet
from .views import PreInvoicingDetailViewSet
from .views import PreInvoicingViewSet
from .views import PrePaidInvoiceViewSet
from .views import PreSubmitInvoiceViewSet
from .views import RecognitionInvoiceViewSet

# 后付费开票
router = SimpleRouter(trailing_slash=False)
router.register(
    "post-paid-invoice",
    PostPaidInvoiceViewSet,
    basename="post_paid_invoice",
)

# 开票记录
invoice_record_router = SimpleRouter(trailing_slash=False)
invoice_record_router.register(
    "invoice-record",
    InvoiceRecordViewSet,
    basename="invoice_record",
)

# 预付费开票
prepaid_router = SimpleRouter(trailing_slash=False)
prepaid_router.register(
    "pre-paid-invoice",
    PrePaidInvoiceViewSet,
    basename="pre_paid_invoice",
)

# 不开票的权责信息
no_invoicing_router = SimpleRouter(trailing_slash=False)
no_invoicing_router.register(
    "no-invoicing",
    NoInvoicingViewSet,
    basename="no_invoicing",
)

# 预开票
pre_invoicing_router = SimpleRouter(trailing_slash=False)
pre_invoicing_router.register(
    "pre-invoicing",
    PreInvoicingViewSet,
    basename="pre_invoicing",
)

# 预开票详情
pre_invoicing_detail_router = SimpleRouter(trailing_slash=False)
pre_invoicing_detail_router.register(
    "pre-invoicing-detail",
    PreInvoicingDetailViewSet,
    basename="pre_invoicing_detail",
)

# 预提交发票管理
pre_submit_router = SimpleRouter(trailing_slash=False)
pre_submit_router.register(
    "pre-submit",
    PreSubmitInvoiceViewSet,
    basename="pre_submit_invoice",
)

# 认款发票管理
recognition_router = SimpleRouter(trailing_slash=False)
recognition_router.register(
    "recognition",
    RecognitionInvoiceViewSet,
    basename="recognition_invoice",
)

# 发票审核
invoice_audit_router = SimpleRouter(trailing_slash=False)
invoice_audit_router.register(
    "invoice-audit",
    InvoiceAuditViewSet,
    basename="invoice_audit",
)

urlpatterns = [
    path("", include(router.urls)),
    path("", include(invoice_record_router.urls)),
    path("", include(prepaid_router.urls)),
    path("", include(no_invoicing_router.urls)),
    path("", include(pre_invoicing_router.urls)),
    path("pre-invoicing/<int:invoice_id>/", include(pre_invoicing_detail_router.urls)),
    path("", include(pre_submit_router.urls)),
    path("", include(recognition_router.urls)),
    path("", include(invoice_audit_router.urls)),
]
