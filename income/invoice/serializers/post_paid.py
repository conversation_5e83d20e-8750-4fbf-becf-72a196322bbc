from django.utils import timezone
from rest_framework import serializers

from income import message
from income.charge.models import IncomeChargeDetail
from income.customer.models import IncomeInvoiceInfo
from income.invoice.models import IncomeInvoicingRecord


class PostPaidInvoiceSerializer(serializers.ModelSerializer):
    customer_name = serializers.CharField(read_only=True, help_text="客户名称")
    adjust_month = serializers.IntegerField(read_only=True, help_text="调整账期")
    adjust_reason = serializers.CharField(read_only=True, help_text="调账原因")
    invoice_amount = serializers.DecimalField(
        max_digits=12,
        decimal_places=2,
        read_only=True,
        help_text="开票金额",
    )

    class Meta:
        model = IncomeChargeDetail
        fields = (
            "id",
            "sub_order_no",
            "account_seq",
            "charge_month",
            "fee_amount",
            "tax",
            "tax_type",
            "product_main_category",
            "fee_type",
            "currency_type",
            "customer_name",
            "adjust_month",
            "adjust_reason",
            "invoice_amount",
        )


class PostPaidBatchInvoiceIssuanceSerializer(serializers.Serializer):
    # 客户编号和分账序号可不选择
    customer_num = serializers.CharField(
        help_text="客户编号",
        required=False,
        allow_blank=True,
        allow_null=True,
    )
    account_seq = serializers.CharField(
        help_text="分账序号",
        required=False,
        allow_blank=True,
        allow_null=True,
    )
    # 账期区间, 必填
    start_charge_month = serializers.IntegerField(help_text="起始账期", required=True)
    end_charge_month = serializers.IntegerField(help_text="结束账期", required=True)

    def validate(self, attrs):
        if attrs["start_charge_month"] > attrs["end_charge_month"]:
            raise serializers.ValidationError(
                {"start_charge_month": message.INVALID_CHARGE_MONTH_RANGE},
            )
        return attrs


class PostPaidInvoiceIssuanceSerializer(serializers.Serializer):
    charge_detail_ids = serializers.ListField(
        child=serializers.IntegerField(),
        help_text="权责ID列表",
        required=True,
    )
    exchange_rate = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text="汇率",
        required=True,
    )
    signing_entity = serializers.CharField(
        help_text="开具实体",
        required=True,
    )
    invoice_currency_type = serializers.CharField(
        help_text="开票币种",
        required=True,
    )
    invoice_info_id = serializers.IntegerField(
        help_text="发票信息ID",
        required=True,
    )

    def validate_charge_detail_ids(self, value):
        # 校验权责信息的分账序号是否相同
        account_seqs = set(
            IncomeChargeDetail.objects.filter(id__in=value).values_list(
                "account_seq",
                flat=True,
            ),
        )
        if len(account_seqs) != 1:
            raise serializers.ValidationError(message.ACCOUNT_SEQ_NOT_SAME)
        return value

    def validate(self, attrs):
        # 校验发票信息是否存在
        if not IncomeInvoiceInfo.objects.filter(
            id=attrs["invoice_info_id"],
        ).exists():
            raise serializers.ValidationError(
                {
                    "invoice_info_id": message.INVOICE_INFO_NOT_FOUND,
                },
            )
        return super().validate(attrs)


class InvoiceRecordSerializer(serializers.ModelSerializer):
    class Meta:
        model = IncomeInvoicingRecord
        exclude = ("created_at",)


class OffIssuanceSerializer(serializers.Serializer):
    charge_ids = serializers.ListField(
        child=serializers.IntegerField(),
        help_text="权责ID列表",
        required=True,
    )

    def create(self, validated_data):
        # 将need_invoice设置为0
        IncomeChargeDetail.objects.filter(id__in=validated_data["charge_ids"]).update(
            need_invoice=0,
            updated_at=timezone.now(),
        )
        return {"message": "关闭开票成功"}

    def to_representation(self, data):
        return data
