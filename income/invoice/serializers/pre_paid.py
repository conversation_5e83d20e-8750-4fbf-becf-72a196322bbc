from django.db import transaction
from rest_framework import serializers

from income import const
from income.invoice.models import IncomeInvoice
from income.invoice.models import IncomeInvoiceDetail
from income.invoice.utils import generate_invoice_no
from income.order.models import IncomeOrderInfo


class PrePaidInvoiceSerializer(serializers.ModelSerializer):
    class Meta:
        model = IncomeOrderInfo
        fields = (
            "id",
            "order_num",
            "total_num",
            "account_seq",
            "bill_status",
            "customer_num",
            "product_main_category",
            "pay_cycle",
            "currency_type",
        )


class OrderInfoInvoicingSerializer(serializers.Serializer):
    """订单填写的开票信息"""

    amount = serializers.DecimalField(
        max_digits=12,
        decimal_places=2,
        help_text="开票金额",
        required=True,
    )
    invoice_month = serializers.IntegerField(
        help_text="开票账期",
        required=True,
    )


class PrePaidInvoicingOrderInfoSerializer(serializers.Serializer):
    """预付费开票的订单相关信息"""

    order_id = serializers.IntegerField(help_text="订单ID", required=True, min_value=1)
    order_info = serializers.ListField(
        child=OrderInfoInvoicingSerializer(),
        help_text="订单填写的信息列表",
        required=True,
    )


class PrePaidInvoiceIssuanceSerializer(serializers.ModelSerializer):
    """预付费开票的开票信息"""

    orders = serializers.ListSerializer(
        child=PrePaidInvoicingOrderInfoSerializer(),
        help_text="订单信息列表",
        required=True,
    )

    class Meta:
        model = IncomeInvoice
        fields = (
            "orders",
            "amount",
            "account_seq",
            "invoice_info_id",
            "invoice_type",
            "tax_rate",
            "tax_amount",
            "currency_type",
            "invoice_currency_type",
            "exchange_rate",
            "signing_entity",
            "customer_num",
            "remark",
        )

    def create(self, validated_data):
        # 获取订单信息
        orders_data = validated_data.pop("orders")
        # 生成发票号
        validated_data["invoice_no"] = generate_invoice_no(
            validated_data["account_seq"],
        )
        validated_data["create_user"] = self.context["request"].user.username
        validated_data["invoice_pay_type"] = const.InvoicePayType.PREPAID
        # 使用事务确保数据一致性
        with transaction.atomic():
            # 创建发票记录
            invoice = IncomeInvoice.objects.create(**validated_data)

            # 创建发票明细记录
            invoice_detail_objects = []

            for order_data in orders_data:
                order_id = order_data["order_id"]
                order_info_list = order_data["order_info"]

                for order_info in order_info_list:
                    invoice_detail = IncomeInvoiceDetail(
                        invoice_id=invoice.id,
                        target_id=order_id,
                        target_type=const.InvoiceTargetType.ORDER,
                        amount=order_info["amount"],
                        invoice_month=order_info["invoice_month"],
                    )
                    invoice_detail_objects.append(invoice_detail)

            # 批量创建发票明细记录
            IncomeInvoiceDetail.objects.bulk_create(
                invoice_detail_objects,
                batch_size=500,
            )

        return {"message": "预付费开票成功"}

    def to_representation(self, data):
        return data
