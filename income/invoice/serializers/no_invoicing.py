from django.utils import timezone
from rest_framework import serializers

from income.charge.models import IncomeChargeDetail


class NoInvoicingSerializer(serializers.ModelSerializer):
    customer_name = serializers.CharField(read_only=True, help_text="客户名称")
    adjust_month = serializers.IntegerField(read_only=True, help_text="调整账期")

    class Meta:
        model = IncomeChargeDetail
        fields = (
            "id",
            "sub_order_no",
            "account_seq",
            "charge_month",
            "fee_amount",
            "tax",
            "tax_type",
            "income_type",
            "pay_type",
            "customer_name",
            "adjust_month",
        )


class OnIssuanceSerializer(serializers.Serializer):
    charge_ids = serializers.ListField(
        child=serializers.IntegerField(),
        help_text="权责ID列表",
        required=True,
    )

    def create(self, validated_data):
        # 将need_invoice设置为1
        IncomeChargeDetail.objects.filter(id__in=validated_data["charge_ids"]).update(
            need_invoice=1,
            updated_at=timezone.now(),
        )
        return {"message": "打开开票"}

    def to_representation(self, data):
        return data
