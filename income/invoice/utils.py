import time
from datetime import datetime

import pandas as pd
from django.db.models.query import RawQuerySet
from django.http import HttpResponse
from django.utils import timezone
from django.utils.encoding import escape_uri_path

from income import const
from income.customer.models import CustomerInfo
from income.customer.models import IncomeAccountSeq
from income.customer.models import IncomeInvoiceInfo
from income.utils.common import partial_register
from income.utils.excel_export import ExcelExporter

from .models import IncomeInvoice
from .models import IncomeInvoiceDetail


def generate_invoice_no(account_seq: str):
    """生成发票号"""
    # 使用时间戳生成唯一发票号, 确保不超过20个字符
    timestamp = int(time.time() * 1000)
    return f"{account_seq}#{timestamp}"


def get_account_seq_tax_map(grouped_df):
    """获取分账序号对应的税率"""
    res = IncomeAccountSeq.objects.filter(
        account_seq__in=grouped_df["account_seq"],
    ).values("account_seq", "tax")
    return dict(res)


def get_invoice_info(account_seq: str):
    """获取发票信息"""
    return (
        IncomeInvoiceInfo.objects.filter(
            account_seq=account_seq,
            priority=const.InvoiceInfoPriority.HIGH,
        )
        .values_list("id", "customer_invoice_type")
        .last()
    )


def add_extra_field(query_data_list: list):
    """添加额外字段

    :param query_data_list:
    :return:
    """
    customer_nums = [query_data["customer_num"] for query_data in query_data_list]
    customer_qs = CustomerInfo.objects.filter(
        customer_num__in=set(customer_nums),
    ).values_list("customer_num", "customer_name")
    customer_map = dict(customer_qs)
    for query_data in query_data_list:
        query_data["customer_name"] = customer_map.get(query_data.pop("customer_num"))
    return query_data_list


def invoice_add_extra_field(query_data_list: list):
    """添加额外字段

    :param query_data_list:
    :return:
    """
    # 获取客户名称
    customer_nums = [query_data["customer_num"] for query_data in query_data_list]
    customer_qs = CustomerInfo.objects.filter(
        customer_num__in=set(customer_nums),
    ).values_list("customer_num", "customer_name")
    customer_map = dict(customer_qs)
    # 获取发票信息
    invoice_info_ids = [query_data["invoice_info_id"] for query_data in query_data_list]
    invoice_info_qs = IncomeInvoiceInfo.objects.filter(
        id__in=set(invoice_info_ids),
    ).values_list("id", "customer_invoice_name")
    invoice_info_map = dict(invoice_info_qs)
    for query_data in query_data_list:
        query_data["customer_name"] = customer_map.get(query_data.pop("customer_num"))
        query_data["customer_invoice_name"] = invoice_info_map.get(
            query_data.pop("invoice_info_id"),
        )
        query_data["created_at"] = query_data["created_at"].strftime("%s")
    return query_data_list


INVOICE_ISSUANCE_MAP = {}
_register = partial_register(INVOICE_ISSUANCE_MAP)


@_register(const.InvoiceIssuanceAction.ISSUANCE)
def invoice_issuance(data: dict):
    """开票:
        将发票状态更新为已开票

    :param data: 开票数据
    :return:
    """
    invoice_ids = data["invoice_ids"]
    IncomeInvoice.objects.filter(id__in=invoice_ids).update(
        state=const.InvoiceState.ISSUED,
        group_approved_state=const.InvoiceApprovedState.DRAFT,
        updated_at=timezone.now(),
    )


@_register(const.InvoiceIssuanceAction.PAUSE)
def invoice_pause(data: dict):
    """暂不开票:
        将发票状态更新为暂不开票
        同时将明细状态更新为失效

    :param data: 开票数据
    :return:
    """
    invoice_ids = data["invoice_ids"]
    IncomeInvoice.objects.filter(id__in=invoice_ids).update(
        state=const.InvoiceState.PAUSED,
        updated_at=timezone.now(),
    )
    IncomeInvoiceDetail.objects.filter(invoice_id__in=invoice_ids).update(
        state=const.InvoiceDetailState.INVALID,
        updated_at=timezone.now(),
    )


def generate_file_response(file_data, file_prefix):
    """生成文件响应"""

    # 生成文件名
    filename = f"{file_prefix}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    response = HttpResponse(
        content=file_data,
        content_type="application/vnd.ms-excel",
    )
    response["Content-Disposition"] = (
        f'attachment; filename="{escape_uri_path(filename)}"'
    )
    return response


def export_to_excel(queryset: RawQuerySet):
    """导出预开票的数据为Excel文件

    Args:
        queryset (RawQuerySet): 筛选后的数据

    Returns:
        _type_: HttpResponse
    """
    data_list = [row.__dict__ for row in queryset]
    drop_labels = ["_state", "id"]
    # 创建DataFrame
    if not data_list:
        # 如果没有数据,创建空的DataFrame
        pre_invoicing_df = pd.DataFrame(
            columns=list(const.INVOICE_FILED_MAPPING.keys()) + drop_labels,
        )
    else:
        pre_invoicing_df = pd.DataFrame(data_list)
    pre_invoicing_df = pre_invoicing_df.drop(
        labels=["_state", "id"],
        axis=1,
    )
    pre_invoicing_df["state"] = pre_invoicing_df["state"].map(
        dict(const.InvoiceState.choices),
    )
    pre_invoicing_df = pre_invoicing_df.rename(columns=const.INVOICE_FILED_MAPPING)
    # 使用Excel导出工具
    exporter = ExcelExporter()
    excel_file = exporter.export_data(
        data_df=pre_invoicing_df,
        sheet_name="预开票信息",
    )
    return generate_file_response(
        file_data=excel_file,
        file_prefix="预开票信息",
    )


def group_approved_state_export_excel(queryset, file_prefix="待提交"):
    """待提交页面数据导出Excel

    :param queryset: RawQueryset
    :return: HttpResponse
    """

    data_list = [row.__dict__ for row in queryset]
    drop_labels = ["_state", "id"]
    # 表头映射修改
    mapping = const.INVOICE_FILED_MAPPING.copy()
    mapping["group_approved_state"] = "审批状态"
    del mapping["state"]
    # 创建DataFrame
    if not data_list:
        # 如果没有数据,创建空的DataFrame
        pre_submit_df = pd.DataFrame(
            columns=list(mapping.keys()) + drop_labels,
        )
    else:
        pre_submit_df = pd.DataFrame(data_list)
    pre_submit_df = pre_submit_df.drop(
        labels=["_state", "id"],
        axis=1,
    )
    pre_submit_df["group_approved_state"] = pre_submit_df["group_approved_state"].map(
        dict(const.InvoiceApprovedState.choices),
    )
    pre_submit_df = pre_submit_df.rename(columns=mapping)
    # 使用Excel导出工具
    exporter = ExcelExporter()
    excel_file = exporter.export_data(
        data_df=pre_submit_df,
        sheet_name=f"{file_prefix}信息",
    )
    return generate_file_response(
        file_data=excel_file,
        file_prefix=file_prefix,
    )
