import logging
import uuid
from decimal import Decimal
from typing import Any

import pandas as pd
from django.db import transaction

from income import const
from income.charge.models import IncomeChargeDetail
from income.contract.models import ContractInfo
from income.customer.models import IncomeInvoiceInfo

from .models import IncomeInvoice
from .models import IncomeInvoiceDetail
from .models import IncomeInvoicingRecord
from .utils import generate_invoice_no
from .utils import get_account_seq_tax_map
from .utils import get_invoice_info

logger = logging.getLogger("invoice")

# SQL查询常量
BASE_INVOICE_QUERY = """
SELECT
    a.`id`,
    a.`account_seq`,
    a.`fee_amount`,
    a.`currency_type`,
    a.`customer_num`,
    a.`contract_num`,
    a.`charge_month`,
    COALESCE(d.`invoice_amount`, 0) AS invoice_amount
FROM
    `income_charge_detail` a
    LEFT JOIN (
        SELECT
            `target_id`,
            sum(`amount`) AS invoice_amount
        FROM
            `income_invoice_detail`
        WHERE
            target_type = 'bill'
            AND state = 1
        GROUP BY
            `target_id`) d ON a.id = d.`target_id`
WHERE
    a.`pay_type` = '后付'
    AND (d.invoice_amount IS NULL OR a.`fee_amount` != d.invoice_amount)
    AND a.`need_invoice` = 1
"""


class Issuance:
    """开票基类,定义通用的开票逻辑"""

    def get_request_info(self) -> dict[str, Any]:
        raise NotImplementedError

    def get_current_amount(self, total_amount: Decimal) -> Decimal | None:
        raise NotImplementedError

    def create_invoice_record(self, **kwargs) -> IncomeInvoicingRecord:
        raise NotImplementedError

    def get_queryset(self):
        """获取查询结果集"""
        raise NotImplementedError

    def _get_invoice_info(self, account_seq: str):
        """获取发票信息"""
        raise NotImplementedError

    def _get_signing_entity(self, contract_num: str | None):
        """获取签约主体"""
        raise NotImplementedError

    def _process_dataframe(self, charge_df: pd.DataFrame) -> pd.DataFrame | None:
        """通用的DataFrame处理逻辑"""
        if charge_df.empty:
            logger.info("No data found for invoice processing")
            return None

        # 移除Django内部字段
        if "_state" in charge_df.columns:
            charge_df = charge_df.drop(columns=["_state"])

        # 计算可开票金额
        charge_df["available_amount"] = (
            charge_df["fee_amount"] - charge_df["invoice_amount"]
        )

        # 过滤掉可开票金额为0或负数的记录
        charge_df = charge_df[charge_df["available_amount"] > 0]
        if charge_df.empty:
            logger.info("No data found after filtering available amounts")
            return None

        return charge_df

    def _group_by_account_seq(self, charge_df: pd.DataFrame) -> pd.DataFrame:
        """按分账序号分组汇总数据"""
        return (
            charge_df.groupby("account_seq")
            .agg(
                {
                    "available_amount": "sum",
                    "currency_type": "first",
                    "id": list,
                    "fee_amount": list,
                    "invoice_amount": list,
                    "customer_num": "first",
                    "contract_num": "first",
                },
            )
            .reset_index()
        )

    def _calculate_tax_amount(
        self,
        amount: Decimal,
        tax_rate: Decimal,
    ) -> Decimal:
        """计算税额"""
        if tax_rate <= 0:
            return Decimal("0.00")
        return amount - (amount / (1 + tax_rate / 100))

    def _create_invoice_details(
        self,
        invoice_id: int,
        charge_detail_ids: list[int],
        fee_amounts: list[Decimal],
        invoice_amounts: list[Decimal],
    ) -> None:
        """批量创建发票明细记录"""
        details = [
            IncomeInvoiceDetail(
                invoice_id=invoice_id,
                target_id=charge_detail_id,
                target_type=const.InvoiceTargetType.BILL,
                amount=fee_amounts[i] - invoice_amounts[i],
            )
            for i, charge_detail_id in enumerate(charge_detail_ids)
            if fee_amounts[i] - invoice_amounts[i] > 0
        ]

        if details:
            IncomeInvoiceDetail.objects.bulk_create(details, batch_size=500)

    def process_invoice_data(self, grouped_df: pd.DataFrame, **kwargs) -> None:
        """处理发票数据入库

        :param grouped_df: 按照account_seq分组后的数据
        :param kwargs: 其余的特定数据
        """
        account_seq_tax_map = get_account_seq_tax_map(grouped_df)
        task_id = str(uuid.uuid4())

        for _, row in grouped_df.iterrows():
            account_seq = row["account_seq"]

            try:
                # 获取发票信息
                invoice_info = self._get_invoice_info(account_seq)
                if not invoice_info:
                    self._create_failed_record(
                        task_id,
                        account_seq,
                        reason=f"分账序号({account_seq})下, 未查询到发票信息",
                    )
                    continue

                total_amount = row["available_amount"]
                current_amount = self.get_current_amount(total_amount)
                currency_type = row["currency_type"]
                charge_detail_ids = row["id"]
                fee_amounts = row["fee_amount"]
                invoice_amounts = row["invoice_amount"]
                customer_num = row["customer_num"]
                contract_num = row["contract_num"]
                signing_entity = self._get_signing_entity(contract_num)
                # 计算税额
                tax_rate = account_seq_tax_map.get(account_seq, 0)
                tax_amount = self._calculate_tax_amount(current_amount, tax_rate)

                with transaction.atomic():
                    # 创建发票记录
                    invoice = IncomeInvoice.objects.create(
                        invoice_no=generate_invoice_no(account_seq),
                        amount=total_amount,
                        current_amount=current_amount,
                        account_seq=account_seq,
                        tax_rate=tax_rate,
                        tax_amount=tax_amount,
                        currency_type=currency_type,
                        invoice_currency_type=kwargs.get(
                            "invoice_currency_type",
                            currency_type,
                        ),
                        exchange_rate=kwargs.get("exchange_rate", 1),
                        customer_num=customer_num,
                        create_user=kwargs.get("create_user"),
                        signing_entity=signing_entity,
                        invoice_info_id=invoice_info[0],
                        invoice_type=invoice_info[1],
                    )

                    # 创建发票明细
                    self._create_invoice_details(
                        invoice.id,
                        charge_detail_ids,
                        fee_amounts,
                        invoice_amounts,
                    )
                    # 创建成功记录
                    self.create_invoice_record(
                        task_id=task_id,
                        account_seq=account_seq,
                        state=const.InvoiceRecordState.SUCCESS,
                    )

            except Exception as e:
                msg = f"处理分账序号 {account_seq} 时发生异常: {e}"
                logger.exception(msg)
                self._create_failed_record(
                    task_id,
                    account_seq,
                    reason=f"处理异常: {e!s}",
                )

    def _create_failed_record(
        self,
        task_id: str,
        account_seq: str,
        reason: str,
    ) -> None:
        """创建失败记录"""
        self.create_invoice_record(
            task_id=task_id,
            account_seq=account_seq,
            state=const.InvoiceRecordState.FAIL,
            reason=reason,
        )

    def handle_queryset(self, queryset) -> None:
        """处理查询结果集的通用逻辑"""
        try:
            charge_df = pd.DataFrame([row.__dict__ for row in queryset])
            charge_df = self._process_dataframe(charge_df)

            if charge_df is None:
                return

            # 应用子类特定的过滤逻辑
            charge_df = self._apply_filters(charge_df)
            if charge_df is None or charge_df.empty:
                logger.info("No data found after applying filters")
                return

            # 按分账序号分组
            grouped = self._group_by_account_seq(charge_df)

            # 处理开票数据
            self.process_invoice_data(grouped, **self._get_process_kwargs())

        except Exception as e:
            msg = f"处理查询结果集时发生异常: {e}"
            logger.exception(msg)

    def _apply_filters(self, charge_df: pd.DataFrame) -> pd.DataFrame | None:
        """应用子类特定的过滤逻辑,子类可重写此方法"""
        return charge_df

    def _get_process_kwargs(self) -> dict[str, Any]:
        """获取处理开票数据时需要的参数,子类可重写此方法"""
        return {}

    def run(self) -> None:
        """执行开票任务"""
        queryset = self.get_queryset()
        self.handle_queryset(queryset)


class InvoiceBatchIssuance(Issuance):
    """
    批量预开票
    开票规则:
        查出来的数据按界⾯条件筛选后,按照分账序号分组,
        累加明细数据⾦额 (权责⾦额-已开票⾦额)

        操作⼊表income_invoice表:
            amount是累加的明细数据⾦额
            tax为分账序号对应的tax
            tax_amount = amount - (amount ÷ (1+tax/100),
            currency_type/invoice_currency_type的值随机挑选⼀条权责的currency_type
            exchange_rate置空,

        操作⼊表income_invoice_detail表:
            invoice_id是上步⼊表的id
            target_id是权责数据的id
            target_type是bill
            amount = 每条的权责⾦额-已开票⾦额
            invoice_month置空
    """

    def __init__(self, data: dict[str, Any]):
        self.start_charge_month = data["start_charge_month"]
        self.end_charge_month = data["end_charge_month"]
        self.customer_num = data.get("customer_num")
        self.account_seq = data.get("account_seq")
        self.create_user = data["create_user"]

    def get_queryset(self):
        return IncomeChargeDetail.objects.raw(
            BASE_INVOICE_QUERY + " ORDER BY a.`created_at` DESC",
        )

    def get_request_info(self) -> dict[str, Any]:
        return {
            "start_charge_month": self.start_charge_month,
            "end_charge_month": self.end_charge_month,
            "customer_num": self.customer_num,
        }

    def get_current_amount(self, total_amount: Decimal) -> Decimal | None:
        return total_amount

    def _get_invoice_info(self, account_seq: str):
        """获取发票信息"""
        return get_invoice_info(account_seq)

    def create_invoice_record(self, **kwargs) -> IncomeInvoicingRecord:
        return IncomeInvoicingRecord.objects.create(
            invoice_type=const.InvoiceRecordType.PRE_BATCH_INVOICE,
            request_info=self.get_request_info(),
            create_user=self.create_user,
            **kwargs,
        )

    def _apply_filters(self, charge_df: pd.DataFrame) -> pd.DataFrame | None:
        """应用批量开票特定的过滤条件"""
        # 1. 对账单周期进行筛选
        charge_df = charge_df[
            (charge_df["charge_month"] >= self.start_charge_month)
            & (charge_df["charge_month"] <= self.end_charge_month)
        ]

        # 2. 如果customer_num有值,先对charge_df筛选
        if self.customer_num:
            charge_df = charge_df[charge_df["customer_num"] == self.customer_num]

        # 3. 如果account_seq有值,同时增加customer_num和account_seq的筛选
        if self.account_seq:
            charge_df = charge_df[charge_df["account_seq"] == self.account_seq]

        return charge_df if not charge_df.empty else None

    def _get_process_kwargs(self) -> dict[str, Any]:
        return {"create_user": self.create_user}

    def _get_signing_entity(self, contract_num):
        return ContractInfo.objects.values_list("sign_contract_entity", flat=True).get(
            contract_num=contract_num,
        )


class InvoiceIssuance(Issuance):
    """单笔开票"""

    def __init__(self, data: dict[str, Any]):
        self.charge_detail_ids = data["charge_detail_ids"]
        self.exchange_rate = data["exchange_rate"]
        self.create_user = data["create_user"]
        self.signing_entity = data["signing_entity"]
        self.invoice_currency_type = data["invoice_currency_type"]
        self.invoice_info_id = data["invoice_info_id"]

    def get_queryset(self):
        return IncomeChargeDetail.objects.raw(
            BASE_INVOICE_QUERY + " AND a.id IN %s ORDER BY a.`created_at` DESC",
            [self.charge_detail_ids],
        )

    def get_request_info(self) -> dict[str, Any]:
        return {
            "exchange_rate": self.exchange_rate,
            "signing_entity": self.signing_entity,
            "invoice_currency_type": self.invoice_currency_type,
            "invoice_info_id": self.invoice_info_id,
        }

    def get_current_amount(self, total_amount: Decimal) -> Decimal:
        return total_amount * Decimal(self.exchange_rate)

    def _get_invoice_info(self, account_seq: str):
        """获取发票信息"""
        return IncomeInvoiceInfo.objects.values_list(
            "id",
            "customer_invoice_type",
        ).get(
            pk=self.invoice_info_id,
        )

    def create_invoice_record(self, **kwargs) -> IncomeInvoicingRecord:
        return IncomeInvoicingRecord.objects.create(
            invoice_type=const.InvoiceRecordType.PRE_INVOICE,
            request_info=self.get_request_info(),
            create_user=self.create_user,
            **kwargs,
        )

    def _get_process_kwargs(self) -> dict[str, Any]:
        return {
            "exchange_rate": self.exchange_rate,
            "create_user": self.create_user,
            "signing_entity": self.signing_entity,
            "invoice_currency_type": self.invoice_currency_type,
            "invoice_info_id": self.invoice_info_id,
        }

    def _get_signing_entity(self, contract_num):
        return self.signing_entity
