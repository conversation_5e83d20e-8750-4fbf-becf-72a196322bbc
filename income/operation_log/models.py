from django.db import models

from income import const
from income.utils.common import OperationLogJSONEncoder


class OperationLog(models.Model):
    operator = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        db_comment="用户名",
    )
    operate_dept = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        db_comment="部门名称(冗余存储)",
    )
    operation_type = models.CharField(
        max_length=50,
        choices=const.OperationType.choices,
        blank=True,
        null=True,
        db_comment="操作类型: 新增/修改/删除",
    )
    table_name = models.CharField(
        max_length=50,
        choices=const.OperationLogTable.choices,
        db_comment="模块名称",
    )
    table_id = models.IntegerField(blank=True, null=True, db_comment="模块ID")
    operation_content = models.JSONField(
        blank=True,
        null=True,
        db_comment="操作详情",
        encoder=OperationLogJSONEncoder,
    )
    ip_address = models.Char<PERSON>ield(
        max_length=45,
        blank=True,
        null=True,
        db_comment="IP地址",
    )

    created_at = models.DateTimeField(db_comment="创建时间", auto_now_add=True)

    class Meta:
        managed = False
        db_table = "operation_log"
        db_table_comment = "系统操作日志表"
        ordering = ("-created_at",)

    def __str__(self):
        return f"<OperationLog: {self.id}>"
