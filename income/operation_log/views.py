from rest_framework import mixins

from income.contrib.drf.filters import SearchAnd<PERSON>ilter
from income.contrib.drf.views import GenericViewSet
from income.permissions import IsAuthenticated

from .models import OperationLog
from .serializers import OperationLogSerializer


class OperationLogViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
):
    """操作日志"""

    serializer_class = OperationLogSerializer
    permission_classes = [IsAuthenticated]

    filter_backends = [SearchAndFilter]
    search_fields = ["operation_type", "table_name"]
    search_contains = True

    def get_queryset(self):
        return OperationLog.objects.filter(operator=self.request.user.username)
