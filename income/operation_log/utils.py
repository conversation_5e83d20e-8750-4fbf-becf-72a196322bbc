import logging

from django.db import models
from django.db.models.query import QuerySet

from income import const
from income.contract.models import ContractInfo
from income.customer.models import ContactInfo
from income.customer.models import CustomerInfo
from income.operation_log.models import OperationLog
from income.order.models import IncomeOrderInfo
from income.users.models import UserDepartment

logger = logging.getLogger("operation_log")


def get_field_display_value(instance, field_name, value):
    """获取字段的显示值(处理选择字段)"""
    if value is None:
        return ""

    # 尝试获取选择字段的显示值
    display_method = f"get_{field_name}_display"
    if hasattr(instance, display_method):
        return getattr(instance, display_method)()

    return str(value)


def get_client_ip(request):
    """获取客户端IP地址"""
    x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
    if x_forwarded_for:
        ip = x_forwarded_for.split(",")[0]
    else:
        ip = request.META.get("REMOTE_ADDR")
    return ip


class OperationLogManager:
    """操作日志管理器"""

    def __init__(self):
        self.model_name = None

    def log_operation(
        self,
        request,
        instance_or_queryset,
        operation_type,
        old_instance=None,
        operation_content=None,
    ):
        # 获取模型的名称
        if (model := type(instance_or_queryset)) == QuerySet:
            model = instance_or_queryset.model
        self.model_name = model.__name__
        sub_class = OperationLogManager.__subclasses__()
        sub_class_by_name = {item.__name__: item for item in sub_class}
        real_model = sub_class_by_name.get(f"{self.model_name}LogManager", None)
        if not real_model:
            msg = f"{self.model_name}LogManager MODULE NOT EXISTS !"
            logger.error(msg)
            return None
        return real_model().log(
            request,
            instance_or_queryset,
            operation_type,
            old_instance=old_instance,
            operation_content=operation_content,
        )

    def create_operation_log(
        self,
        operator,
        operate_dept=None,
        operation_type=None,
        table_name=None,
        table_id=None,
        operation_content=None,
        ip_address=None,
    ):
        """创建操作日志记录

        Args:
            operator: 操作人用户名
            operate_dept: 部门名称
            operation_type: 操作类型 (CREATE/UPDATE/DELETE等)
            table_name: 表名/模块名
            table_id: 表记录ID
            operation_content: 操作详情描述
            ip_address: IP地址
        """
        OperationLog.objects.create(
            operator=operator,
            operate_dept=operate_dept,
            operation_type=operation_type,
            table_name=table_name,
            table_id=table_id,
            operation_content=operation_content,
            ip_address=ip_address,
        )

    def compare_model_fields(self, old_instance, new_instance, field_name_map):
        """比较模型实例的字段变更

        Args:
            old_instance: 修改前的实例
            new_instance: 修改后的实例
            field_name_map: 字段名称映射字典

        Returns:
            list: 变更记录列表
        """
        changes = []

        # 获取模型的所有字段
        fields = [field.name for field in new_instance._meta.fields]  # noqa: SLF001

        for field_name in fields:
            # 跳过自动更新的时间字段
            if field_name in ["created_at", "updated_at", "deleted_at"]:
                continue

            old_value = getattr(old_instance, field_name, None)
            new_value = getattr(new_instance, field_name, None)

            # 比较值是否发生变化
            if old_value != new_value:
                # 获取字段的中文名称
                field_display_name = field_name_map.get(field_name, field_name)

                changes.append(
                    {
                        "field_name": field_display_name,
                        "old_value": str(old_value),
                        "new_value": str(new_value),
                    },
                )
        return changes

    def get_field_comment_mapping(self, model_name):
        mapping = {}
        for field in model_name._meta.get_fields():  # noqa: SLF001
            if isinstance(field, models.Field):
                # 获取 db_comment, 若无则使用字段名作为中文名称
                db_comment = getattr(field, "db_comment", None) or field.name
                mapping[field.name] = db_comment
        return mapping

    def log(self, *args, **kwargs):
        """记录操作日志"""
        msg = f"{self.__class__.__name__} MODULE NOT EXISTS LOG FUNCTION!"
        logger.error(msg)


class CustomerInfoLogManager(OperationLogManager):
    """客户信息日志管理器"""

    def log(
        self,
        request,
        instance,
        operation_type,
        old_instance=None,
        operation_content=None,
    ):
        """记录客户信息操作日志

        Args:
            request: HTTP请求对象
            instance: 客户信息实例
            operation_type: 操作类型
            old_instance: 修改前的实例(更新操作时需要)
            operation_content: 操作内容描述(可选)
        """
        if operation_type == const.OperationType.UPDATE and old_instance:
            # 记录字段变更详情
            changes = self.compare_model_fields(
                old_instance,
                instance,
                self.get_field_comment_mapping(CustomerInfo),
            )
            operation_content = changes if changes else None
        elif operation_type == const.OperationType.CREATE:
            operation_content = f"新建客户-{instance.customer_name}"

        self.create_operation_log(
            operator=request.user.username,
            operation_type=operation_type,
            table_name=const.OperationLogTable.CUSTOMER,
            table_id=instance.id,
            operation_content=operation_content,
            ip_address=get_client_ip(request),
        )


class ContactInfoLogManager(OperationLogManager):
    """联系人信息日志管理器"""

    def log(
        self,
        request,
        instance,
        operation_type,
        old_instance=None,
        operation_content=None,
    ):
        """记录联系人信息操作日志

        Args:
            request: HTTP请求对象
            instance: 联系人信息实例
            operation_type: 操作类型
            old_instance: 修改前的实例(更新操作时需要)
            operation_content: 操作内容描述(可选)
        """
        if operation_type == const.OperationType.UPDATE and old_instance:
            # 记录字段变更详情
            changes = self.compare_model_fields(
                old_instance,
                instance,
                self.get_field_comment_mapping(ContactInfo),
            )
            operation_content = changes if changes else None
        elif operation_type == const.OperationType.CREATE:
            operation_content = f"新建联系人-{instance.name}"

        self.create_operation_log(
            operator=request.user.username,
            operation_type=operation_type,
            table_name=const.OperationLogTable.CONTACT,
            table_id=instance.customer_id,  # 使用客户ID作为关联ID
            operation_content=operation_content,
            ip_address=get_client_ip(request),
        )


class ContractInfoLogManager(OperationLogManager):
    """合同信息日志管理器"""

    def log(
        self,
        request,
        instance,
        operation_type,
        old_instance=None,
        operation_content=None,
    ):
        """记录合同信息操作日志

        Args:
            request: HTTP请求对象
            instance: 合同信息实例
            operation_type: 操作类型
            old_instance: 修改前的实例(更新操作时需要)
            operation_content: 操作内容描述(可选)
        """
        if operation_type == const.OperationType.UPDATE and old_instance:
            # 记录字段变更详情
            changes = self.compare_model_fields(
                old_instance,
                instance,
                self.get_field_comment_mapping(ContractInfo),
            )
            operation_content = changes if changes else None
        elif operation_type == const.OperationType.CREATE:
            operation_content = f"新建合同-{instance.contract_title}"

        self.create_operation_log(
            operator=request.user.username,
            operation_type=operation_type,
            table_name=const.OperationLogTable.CONTRACT,
            table_id=instance.id,
            operation_content=operation_content,
            ip_address=get_client_ip(request),
        )


class IncomeOrderInfoLogManager(OperationLogManager):
    """收入订单日志管理器"""

    def log(
        self,
        request,
        instance,
        operation_type,
        old_instance=None,
        operation_content=None,
    ):
        """记录收入订单操作日志

        Args:
            request: HTTP请求对象
            instance: 订单信息实例
            operation_type: 操作类型
            old_instance: 修改前的实例(更新操作时需要)
            operation_content: 操作内容描述(可选)
        """
        if operation_type == const.OperationType.UPDATE and old_instance:
            # 记录字段变更详情
            changes = self.compare_model_fields(
                old_instance,
                instance,
                self.get_field_comment_mapping(IncomeOrderInfo),
            )
            operation_content = changes if changes else None
        elif operation_type == const.OperationType.CREATE:
            operation_content = f"新建订单-{instance.total_num}"

        self.create_operation_log(
            operator=request.user.username,
            operation_type=operation_type,
            table_name=const.OperationLogTable.ORDER,
            table_id=instance.id,
            operation_content=operation_content,
            ip_address=get_client_ip(request),
        )


class UserLogManager(OperationLogManager):
    """用户日志管理器"""

    def log(
        self,
        request,
        instance,
        operation_type,
        old_instance=None,
        operation_content=None,
    ):
        """记录用户操作日志

        Args:
            request: HTTP请求对象
            instance: 用户实例
            operation_type: 操作类型
            old_instance: 修改前的实例(更新操作时需要)
            operation_content: 操作内容描述(可选)
        """
        if operation_type == const.OperationType.CREATE:
            operation_content = f"新建用户-{instance.username}"

        self.create_operation_log(
            operator=request.user.username,
            operation_type=operation_type,
            table_name=const.OperationLogTable.USER,
            table_id=instance.id,
            operation_content=operation_content,
            ip_address=get_client_ip(request),
        )


class UserDepartmentLogManager(OperationLogManager):
    """部门日志管理器"""

    def log(
        self,
        request,
        instance,
        operation_type,
        old_instance=None,
        operation_content=None,
    ):
        """记录部门操作日志

        Args:
            request: HTTP请求对象
            instance: 部门实例
            operation_type: 操作类型
            old_instance: 修改前的实例(更新操作时需要)
            operation_content: 操作内容描述(可选)
        """
        if operation_type == const.OperationType.UPDATE and old_instance:
            # 记录字段变更详情
            changes = self.compare_model_fields(
                old_instance,
                instance,
                self.get_field_comment_mapping(UserDepartment),
            )
            operation_content = changes if changes else None
        elif operation_type == const.OperationType.CREATE:
            operation_content = f"新建部门-{instance.department_name}"

        self.create_operation_log(
            operator=request.user.username,
            operation_type=operation_type,
            table_name=const.OperationLogTable.USER_DEPARTMENT,
            table_id=instance.id,
            operation_content=operation_content,
            ip_address=get_client_ip(request),
        )
