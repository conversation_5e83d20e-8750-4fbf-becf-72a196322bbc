import functools
import logging

from rest_framework import exceptions

logger = logging.getLogger("django.request")


def request_log(func):
    # 重要的地方加入请求日志
    @functools.wraps(func)
    def inner(request, *args, **kwargs):
        response = func(request, *args, **kwargs)
        log_data = {}
        if request.method in ["POST", "PUT", "PATCH"]:
            try:
                if request.FILES:
                    log_data["FILES"] = request.FILES
                if request.POST:
                    log_data["FORM"] = request.POST
                else:
                    log_data["BODY"] = request.data
            except exceptions.UnsupportedMediaType:
                log_data["BODY"] = request.body.decode("utf-8")
        msg = "{request.method} {request.get_full_path()}\n{log_data}"
        logger.info(msg)
        return response

    return inner
