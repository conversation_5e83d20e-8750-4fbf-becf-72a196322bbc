import hashlib
import logging
import mimetypes
import uuid
from datetime import datetime
from pathlib import Path
from typing import Any

from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.files.uploadedfile import UploadedFile
from django.http import FileResponse
from django.utils.encoding import escape_uri_path
from PIL import Image
from rest_framework.exceptions import ParseError

from income import message

logger = logging.getLogger("file_handler")


class FileTypeConfig:
    """文件类型配置类"""

    # 常量定义
    MB = 1024 * 1024
    MAX_IMAGE_DIMENSION = 10000
    DEFAULT_MAX_SIZE = 10 * MB

    # 支持的文件类型配置
    SUPPORTED_TYPES = {
        "pdf": {
            "extensions": [".pdf"],
            "mime_types": ["application/pdf"],
            "max_size": 10 * MB,
            "description": "PDF文档",
        },
        "word": {
            "extensions": [".doc", ".docx"],
            "mime_types": [
                "application/msword",
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            ],
            "max_size": 20 * MB,
            "description": "Word文档",
        },
        "excel": {
            "extensions": [".xls", ".xlsx"],
            "mime_types": [
                "application/vnd.ms-excel",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ],
            "max_size": 50 * MB,
            "description": "Excel表格",
        },
        "image": {
            "extensions": [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"],
            "mime_types": [
                "image/jpeg",
                "image/png",
                "image/gif",
                "image/bmp",
                "image/webp",
            ],
            "max_size": 5 * MB,
            "description": "图片文件",
        },
    }

    # 存储路径配置
    STORAGE_CATEGORIES = {
        "customer": "customers",
        "invoice": "invoices",
        "order": "orders",
        "contract": "contracts",
        "receipt": "receipts",
        "fee": "fees",
        "temp": "temp",
    }


class FileValidator:
    """文件验证器"""

    # 错误消息常量
    ERROR_EMPTY_FILENAME = "文件名不能为空"
    ERROR_UNKNOWN_FILE_TYPE = "无法识别文件类型"
    ERROR_UNSUPPORTED_IMAGE_FORMAT = "不支持的图片格式"
    ERROR_IMAGE_TOO_LARGE = "图片尺寸过大"
    ERROR_CORRUPTED_IMAGE = "图片文件损坏或格式错误"

    def __init__(
        self,
        allowed_types: list[str] | None = None,
        max_size: int | None = None,
    ):
        """
        初始化文件验证器

        Args:
            allowed_types: 允许的文件类型列表
            max_size: 最大文件大小(字节)
        """
        self.allowed_types = allowed_types or ["pdf", "word", "excel", "image"]
        self.max_size = max_size

    def validate(self, file: UploadedFile) -> dict[str, Any]:
        """
        验证文件

        Args:
            file: 上传的文件对象

        Returns:
            验证结果字典
        """
        result = {
            "is_valid": True,
            "file_type": None,
            "file_size": file.size,
            "original_name": file.name,
            "errors": [],
        }

        try:
            self._validate_filename(file.name)
            file_ext = self._get_file_extension(file.name)
            file_type = self._validate_file_type(file, file_ext)
            result["file_type"] = file_type

            self._validate_file_size(file, file_type)
            self._validate_file_content(file, file_type)

        except ValidationError as e:
            result["is_valid"] = False
            result["errors"].append(str(e))

        return result

    def _validate_filename(self, filename: str) -> None:
        """验证文件名"""
        if not filename:
            self._raise_validation_error(self.ERROR_EMPTY_FILENAME)

    def _get_file_extension(self, filename: str) -> str:
        """获取文件扩展名"""
        file_ext = Path(filename).suffix.lower()
        if not file_ext:
            self._raise_validation_error(self.ERROR_UNKNOWN_FILE_TYPE)
        return file_ext

    def _validate_file_type(self, file: UploadedFile, file_ext: str) -> str | None:
        """验证文件类型"""
        # 收集所有允许类型的扩展名和MIME类型
        allowed_extensions = []
        allowed_mime_types = []

        for type_name in self.allowed_types:
            type_config = FileTypeConfig.SUPPORTED_TYPES.get(type_name)
            if type_config:
                allowed_extensions.extend(type_config["extensions"])
                allowed_mime_types.extend(type_config["mime_types"])

        # 检查文件扩展名是否在允许范围内
        if file_ext not in allowed_extensions:
            self._raise_validation_error(f"不支持的文件类型: {file_ext}")

        # 获取文件的MIME类型并验证
        mime_type, _ = mimetypes.guess_type(file.name)
        if not mime_type or mime_type not in allowed_mime_types:
            self._raise_validation_error(f"文件MIME类型不匹配: {mime_type}")

        # 确定具体的文件类型
        for type_name in self.allowed_types:
            type_config = FileTypeConfig.SUPPORTED_TYPES.get(type_name)
            if (
                type_config
                and file_ext in type_config["extensions"]
                and mime_type in type_config["mime_types"]
            ):
                return type_name

        return None

    def _validate_file_size(self, file: UploadedFile, file_type: str) -> None:
        """验证文件大小"""
        max_size = self.max_size
        if not max_size:
            type_config = FileTypeConfig.SUPPORTED_TYPES.get(file_type)
            max_size = (
                type_config["max_size"]
                if type_config
                else FileTypeConfig.DEFAULT_MAX_SIZE
            )

        if file.size > max_size:
            max_size_mb = max_size / FileTypeConfig.MB
            self._raise_validation_error(f"文件大小超过限制 ({max_size_mb:.1f}MB)")

    def _validate_file_content(self, file: UploadedFile, file_type: str) -> None:
        """验证文件内容"""
        if file_type == "image":
            self._validate_image_content(file)

    def _validate_image_content(self, file: UploadedFile) -> None:
        """验证图片文件内容"""
        try:
            file.seek(0)
            with Image.open(file) as img:
                self._validate_image_format(img)
                self._validate_image_dimensions(img)
            file.seek(0)
        except ValidationError:
            raise
        except Exception as e:
            msg = f"图片验证失败: {e}"
            logger.exception(msg)
            self._raise_validation_error(self.ERROR_CORRUPTED_IMAGE)

    def _validate_image_format(self, img: Image.Image) -> None:
        """验证图片格式"""
        if img.format and img.format.lower() not in [
            "jpeg",
            "png",
            "gif",
            "bmp",
            "webp",
        ]:
            self._raise_validation_error(self.ERROR_UNSUPPORTED_IMAGE_FORMAT)

    def _validate_image_dimensions(self, img: Image.Image) -> None:
        """验证图片尺寸"""
        width, height = img.size
        if (
            width > FileTypeConfig.MAX_IMAGE_DIMENSION
            or height > FileTypeConfig.MAX_IMAGE_DIMENSION
        ):
            self._raise_validation_error(self.ERROR_IMAGE_TOO_LARGE)

    def _raise_validation_error(self, message: str) -> None:
        """抛出验证错误"""
        raise ValidationError(message)


class FileStorage:
    """文件存储管理器"""

    def __init__(self, base_path: str | None = None):
        """
        初始化文件存储管理器

        Args:
            base_path: 基础存储路径
        """
        self.base_path = Path(base_path or settings.MEDIA_ROOT)

    def save_file(
        self,
        file: UploadedFile,
        category: str,
        subfolder: str | None = None,
        custom_filename: str | None = None,
    ) -> dict[str, str]:
        """
        保存文件

        Args:
            file: 上传的文件对象
            category: 文件类别 (customer, invoice, order等)
            subfolder: 子文件夹 (可选)
            custom_filename: 自定义文件名 (可选)

        Returns:
            文件信息字典
        """
        storage_path = self._generate_storage_path(category, subfolder)
        filename = custom_filename or self._generate_filename(file.name)

        full_dir = self.base_path / storage_path
        full_dir.mkdir(parents=True, exist_ok=True)

        file_path = full_dir / filename
        self._write_file(file, file_path)

        return {
            "original_name": file.name,
            "saved_name": filename,
            "relative_path": str(storage_path / filename),
            "full_path": str(file_path),
            "file_size": file.size,
            "file_hash": self._calculate_file_hash(file),
        }

    def _generate_storage_path(
        self,
        category: str,
        subfolder: str | None = None,
    ) -> Path:
        """生成存储路径"""
        category_dir = FileTypeConfig.STORAGE_CATEGORIES.get(category, category)

        date_path = Path(category_dir) / datetime.now().strftime("%Y-%m-%d")

        if subfolder:
            date_path = date_path / subfolder

        return date_path

    def _generate_filename(self, original_name: str) -> str:
        """生成唯一文件名"""
        file_ext = Path(original_name).suffix
        filename_hash = hashlib.sha256(original_name.encode()).hexdigest()[:6]
        return f"{uuid.uuid4()!s}_{filename_hash}{file_ext}"

    def _write_file(self, file: UploadedFile, file_path: Path) -> None:
        """写入文件"""
        with file_path.open("wb") as destination:
            for chunk in file.chunks():
                destination.write(chunk)

    def _calculate_file_hash(self, file: UploadedFile) -> str:
        """计算文件哈希值 - 使用SHA256替代MD5"""
        file.seek(0)
        hash_sha256 = hashlib.sha256()
        for chunk in file.chunks():
            hash_sha256.update(chunk)
        file.seek(0)
        return hash_sha256.hexdigest()

    def delete_file(self, file_path: str) -> bool:
        """删除文件"""
        try:
            full_path = self.base_path / file_path
            if full_path.exists():
                full_path.unlink()
                return True
        except OSError as e:
            msg = f"删除文件失败: {file_path}, 错误: {e}"
            logger.exception(msg)
        return False


class FileHandler:
    """通用文件处理器 - 主要接口类"""

    def __init__(self, config: dict[str, Any] | None = None):
        """
        初始化文件处理器

        Args:
            config: 配置字典
        """
        self.config = config or {}
        self.validator = FileValidator(
            allowed_types=self.config.get("allowed_types"),
            max_size=self.config.get("max_size"),
        )
        self.storage = FileStorage(self.config.get("base_path"))

    def process_file(
        self,
        file: UploadedFile,
        category: str,
        subfolder: str | None = None,
        **kwargs,
    ) -> dict[str, Any]:
        """
        处理文件 - 验证并保存

        Args:
            file: 上传的文件对象
            category: 文件类别
            subfolder: 子文件夹 (可选)
            **kwargs: 其他参数

        Returns:
            处理结果字典
        """
        validation_result = self.validator.validate(file)

        if not validation_result["is_valid"]:
            return {
                "success": False,
                "errors": validation_result["errors"],
                "file_info": validation_result,
            }

        try:
            file_info = self.storage.save_file(
                file,
                category,
                subfolder,
                kwargs.get("custom_filename"),
            )
        except Exception as e:
            msg = f"文件保存失败: {e}"
            logger.exception(msg)
            return {
                "success": False,
                "errors": [f"文件保存失败: {e!s}"],
                "file_info": validation_result,
            }
        else:
            return {
                "success": True,
                "file_info": {**validation_result, **file_info},
                "errors": [],
            }

    def delete_file(self, file_path: str) -> bool:
        """删除文件"""
        return self.storage.delete_file(file_path)

    def get_file_info(self, file_path: str) -> dict[str, Any]:
        """获取文件信息"""
        full_path = self.storage.base_path / file_path
        if not full_path.exists():
            return {"exists": False}

        stat = full_path.stat()
        return {
            "exists": True,
            "size": stat.st_size,
            "created_time": datetime.fromtimestamp(stat.st_ctime),
            "modified_time": datetime.fromtimestamp(stat.st_mtime),
            "extension": full_path.suffix,
            "name": full_path.name,
        }


class FileSecurityChecker:
    """文件安全检查器"""

    # 危险文件扩展名
    DANGEROUS_EXTENSIONS = {
        ".exe",
        ".bat",
        ".cmd",
        ".com",
        ".pif",
        ".scr",
        ".vbs",
        ".js",
        ".jar",
        ".app",
        ".deb",
        ".pkg",
        ".dmg",
        ".sh",
        ".ps1",
    }

    # 可疑文件头
    SUSPICIOUS_HEADERS = {
        b"MZ": "Windows executable",
        b"PK": "Archive file (potential executable)",
        b"\x7fELF": "Linux executable",
    }

    @classmethod
    def check_file_security(cls, file: UploadedFile) -> dict[str, Any]:
        """
        检查文件安全性

        Args:
            file: 上传的文件对象

        Returns:
            安全检查结果
        """
        result = {"is_safe": True, "warnings": [], "threats": []}

        # 检查文件扩展名
        file_ext = Path(file.name).suffix.lower()
        if file_ext in cls.DANGEROUS_EXTENSIONS:
            result["is_safe"] = False
            result["threats"].append(f"危险文件类型: {file_ext}")

        # 检查文件头
        file.seek(0)
        header = file.read(4)
        file.seek(0)

        for suspicious_header, description in cls.SUSPICIOUS_HEADERS.items():
            if header.startswith(suspicious_header):
                result["warnings"].append(f"可疑文件头: {description}")

        # 检查文件名中的可疑字符
        suspicious_chars = ["<", ">", "|", "&", ";", "$", "`"]
        if any(char in file.name for char in suspicious_chars):
            result["warnings"].append("文件名包含可疑字符")

        return result


class FileDuplicateChecker:
    """文件重复检查器"""

    def __init__(self, storage: FileStorage):
        self.storage = storage

    def check_duplicate(self, file: UploadedFile, category: str) -> dict[str, Any]:
        """
        检查文件是否重复

        Args:
            file: 上传的文件对象
            category: 文件类别

        Returns:
            重复检查结果
        """
        file_hash = self._calculate_file_hash(file)
        category_dir = FileTypeConfig.STORAGE_CATEGORIES.get(category, category)
        search_path = self.storage.base_path / category_dir

        duplicates = []
        if search_path.exists():
            for file_path in search_path.rglob("*"):
                if file_path.is_file():
                    try:
                        existing_hash = self._calculate_existing_file_hash(file_path)
                        if existing_hash == file_hash:
                            duplicates.append(
                                {
                                    "path": str(
                                        file_path.relative_to(self.storage.base_path),
                                    ),
                                    "size": file_path.stat().st_size,
                                    "created_time": datetime.fromtimestamp(
                                        file_path.stat().st_ctime,
                                    ),
                                },
                            )
                    except OSError as e:
                        msg = f"无法读取文件 {file_path}: {e}"
                        logger.warning(msg)
                        continue

        return {
            "has_duplicates": len(duplicates) > 0,
            "duplicates": duplicates,
            "file_hash": file_hash,
        }

    def _calculate_file_hash(self, file: UploadedFile) -> str:
        """计算上传文件哈希值 - 使用SHA256替代MD5"""
        file.seek(0)
        hash_sha256 = hashlib.sha256()
        for chunk in file.chunks():
            hash_sha256.update(chunk)
        file.seek(0)
        return hash_sha256.hexdigest()

    def _calculate_existing_file_hash(self, file_path: Path) -> str:
        """计算已存在文件的哈希值"""
        hash_sha256 = hashlib.sha256()
        with file_path.open("rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()


class FileCleanupManager:
    """文件清理管理器"""

    def __init__(self, storage: FileStorage):
        self.storage = storage

    def cleanup_old_files(self, category: str, days_old: int = 30) -> dict[str, Any]:
        """
        清理旧文件

        Args:
            category: 文件类别
            days_old: 文件保留天数

        Returns:
            清理结果
        """
        from datetime import timedelta

        cutoff_date = datetime.now() - timedelta(days=days_old)
        category_dir = FileTypeConfig.STORAGE_CATEGORIES.get(category, category)
        search_path = self.storage.base_path / category_dir

        cleaned_files = []
        total_size_freed = 0

        if search_path.exists():
            for file_path in search_path.rglob("*"):
                if file_path.is_file():
                    try:
                        file_time = datetime.fromtimestamp(
                            file_path.stat().st_mtime,
                        )
                        if file_time < cutoff_date:
                            file_size = file_path.stat().st_size
                            file_path.unlink()
                            cleaned_files.append(
                                {
                                    "path": str(
                                        file_path.relative_to(self.storage.base_path),
                                    ),
                                    "size": file_size,
                                    "last_modified": file_time,
                                },
                            )
                            total_size_freed += file_size
                    except OSError as e:
                        msg = f"清理文件失败 {file_path}: {e}"
                        logger.warning(msg)
                        continue

        return {
            "cleaned_count": len(cleaned_files),
            "total_size_freed": total_size_freed,
            "cleaned_files": cleaned_files,
        }

    def cleanup_empty_directories(self, category: str | None = None) -> int:
        """
        清理空目录

        Args:
            category: 文件类别, None表示清理所有

        Returns:
            清理的目录数量
        """
        cleaned_count = 0

        if category:
            category_dir = FileTypeConfig.STORAGE_CATEGORIES.get(category, category)
            search_path = self.storage.base_path / category_dir
            paths_to_check = [search_path] if search_path.exists() else []
        else:
            paths_to_check = [self.storage.base_path]

        for base_path in paths_to_check:
            for dir_path in sorted(
                base_path.rglob("*"),
                key=lambda p: len(str(p)),
                reverse=True,
            ):
                if dir_path.is_dir():
                    try:
                        if not any(dir_path.iterdir()):  # 目录为空
                            dir_path.rmdir()
                            cleaned_count += 1
                    except OSError as e:
                        # 记录日志, 但不中断清理过程
                        msg = f"清理空目录失败 {dir_path}: {e}"
                        logger.warning(msg)
                        continue

        return cleaned_count


# 便捷函数
def create_file_handler(
    allowed_types: list[str] | None = None,
    max_size: int | None = None,
    base_path: str | None = None,
) -> FileHandler:
    """
    创建文件处理器的便捷函数

    Args:
        allowed_types: 允许的文件类型
        max_size: 最大文件大小
        base_path: 基础存储路径

    Returns:
        FileHandler实例
    """
    config = {}
    if allowed_types:
        config["allowed_types"] = allowed_types
    if max_size:
        config["max_size"] = max_size
    if base_path:
        config["base_path"] = base_path

    return FileHandler(config)


def validate_and_save_file(
    file: UploadedFile,
    category: str,
    allowed_types: list[str] | None = None,
    max_size: int | None = None,
    *,
    check_duplicates: bool = True,
    check_security: bool = True,
) -> dict[str, Any]:
    """
    验证并保存文件的便捷函数

    Args:
        file: 上传的文件对象
        category: 文件类别
        allowed_types: 允许的文件类型
        max_size: 最大文件大小
        check_duplicates: 是否进行重复检查
        check_security: 是否进行安全检查

    Returns:
        处理结果
    """
    handler = create_file_handler(allowed_types, max_size)

    result = {"success": False, "file_info": {}, "errors": [], "warnings": []}

    # 安全检查
    if check_security:
        security_result = FileSecurityChecker.check_file_security(file)
        if not security_result["is_safe"]:
            result["errors"].extend(security_result["threats"])
            return result
        result["warnings"].extend(security_result["warnings"])

    # 重复检测
    if check_duplicates:
        duplicate_result = FileDuplicateChecker(handler.storage).check_duplicate(
            file,
            category,
        )
        if duplicate_result["has_duplicates"]:
            result["warnings"].append("文件重复")
            result["duplicate_info"] = duplicate_result
            return result

    # 处理文件
    process_result = handler.process_file(file, category)
    result.update(process_result)

    return result


def file_preview(file_name: str, file_path: str) -> FileResponse:
    """文件预览

    :param file_name: 文件名称
    :param file_path: 文件路径
    :return: FileResponse对象
    """
    base_path = Path(settings.MEDIA_ROOT)
    file_full_path = base_path / file_path
    # 检查文件是否存在
    if not file_full_path.exists():
        raise ParseError(message.ATTACHMENT_FILE_NOT_FOUND)
    # 根据文件类型设置content_type
    content_type = mimetypes.guess_type(file_name)[0]
    # PDF文件可以直接在浏览器中预览
    response = FileResponse(
        Path.open(file_full_path, "rb"),
        content_type=content_type,
        as_attachment=False,
    )
    response["Content-Disposition"] = f'inline; filename="{escape_uri_path(file_name)}"'
    return response
