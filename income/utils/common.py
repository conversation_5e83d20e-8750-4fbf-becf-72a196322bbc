import json
from functools import partial


def register(handle_map: dict, *keys):
    """
    将被装饰对象动态注册到 handler_map 字典中.

    >>> handle_map = {}
    ... @register(handle_map, 1, 2, 3)
    ... def func():
    ...    pass

    >>> handle_map
        {
            1: func
            2: func
            3: func
         }
    """

    def inner(obj):
        for key in keys:
            handle_map[key] = obj
        return obj

    return inner


def partial_register(handle_map):
    return partial(register, handle_map)


class OperationLogJSONEncoder(json.JSONEncoder):
    def __init__(self, *args, **kwargs):
        # 强制设置 ensure_ascii=False 以正确处理中文字符
        kwargs["ensure_ascii"] = False
        super().__init__(*args, **kwargs)

    def default(self, obj):
        """处理特殊对象的序列化"""
        if isinstance(obj, str):
            return obj
        # 其他类型使用默认处理
        return super().default(obj)
